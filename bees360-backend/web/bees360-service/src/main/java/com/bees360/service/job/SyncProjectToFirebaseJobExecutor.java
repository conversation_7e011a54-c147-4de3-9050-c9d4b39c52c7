package com.bees360.service.job;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.firebase.FirebaseApi;
import com.bees360.grpc.GrpcApi;
import com.bees360.job.registry.SyncProjectToFirebaseJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.ProjectService;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.service.firebase.FirebaseService;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.SetOptions;

import io.grpc.StatusRuntimeException;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import org.springframework.stereotype.Component;

import java.io.IOException;

@Log4j2
@Component
@ToString
public class SyncProjectToFirebaseJobExecutor
        extends AbstractJobExecutor<SyncProjectToFirebaseJob> {
    private final FirebaseService firebaseService;
    private final Firestore firestore;
    private final ProjectService projectService;
    private final FirebaseProjectService firebaseProjectService;

    public SyncProjectToFirebaseJobExecutor(
            FirebaseService firebaseService,
            Firestore firestore,
            ProjectService projectService,
            FirebaseProjectService firebaseProjectService) {
        this.firebaseService = firebaseService;
        this.firestore = firestore;
        this.projectService = projectService;
        this.firebaseProjectService = firebaseProjectService;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SyncProjectToFirebaseJob job) throws IOException {
        try {
            var projectId = job.getProjectId();
            var path = firebaseService.getProjectDocumentPath(projectId);
            DocumentReference reference = firestore.document(path);
            var project = projectService.findById(projectId);
            if (project.isEmpty()) {
                // TODO 由于事务问题，Project可能还没有保存到数据库中, 这里需要抛出IOException,触发重试
                throw new IOException("The project '%s' is not found.".formatted(projectId));
            }
            Object data = firebaseProjectService.getAllFieldsInProject(project.get());
            FirebaseApi.exec(() -> reference.set(data, SetOptions.merge()));
            log.info("Success sync project '{}' to firebase.", projectId);
        } catch (StatusRuntimeException e) {
            // TODO 因为目前grpc client没有很好的捕获 StatusRuntimeException,所以这里需要将它转换为系统默认的异常
            throw GrpcApi.translateException(e);
        }
    }
}
