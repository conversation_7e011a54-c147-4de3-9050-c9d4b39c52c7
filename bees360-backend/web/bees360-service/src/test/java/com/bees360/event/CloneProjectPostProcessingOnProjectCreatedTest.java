package com.bees360.event;

import com.bees360.entity.ProjectImage;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.job.JobScheduler;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.registry.ImageCloneJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectImageTagMapper;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.user.User;
import com.google.protobuf.StringValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@SpringJUnitConfig(classes = CloneProjectPostProcessingOnProjectCreatedTest.Config.class)
@TestPropertySource("classpath:application-test.yml")
@SpringBootTest
@EnableConfigurationProperties
class CloneProjectPostProcessingOnProjectCreatedTest {

    @Import({
        RabbitApiConfig.class,
        RabbitEventPublisher.class,
        RabbitEventDispatcher.class,
        RabbitJobDispatcher.class,
        RabbitJobScheduler.class,
    })
    @Configuration
    static class Config {
        @Bean
        ProjectIIManager projectIIManager() {
            return mock(ProjectIIManager.class);
        }

        @Bean
        ProjectImageMapper projectImageMapper() {
            return mock(ProjectImageMapper.class);
        }

        @Bean
        ProjectStatusManager projectStatusManager() {
            return mock(ProjectStatusManager.class);
        }

        @Bean
        ProjectImageTagMapper projectImageTagMapper() {
            return mock(ProjectImageTagMapper.class);
        }
    }

    @Autowired
    private ProjectIIManager projectIIManager;

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Autowired
    private ProjectStatusManager projectStatusManager;

    @Autowired
    private RabbitJobDispatcher rabbitJobDispatcher;

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private ProjectImageTagMapper projectImageTagMapper;

    private CloneProjectPostProcessingOnProjectCreated listener;

    @BeforeEach
    void setUp() {
        Mockito.reset(projectIIManager);
        Mockito.reset(projectImageMapper);
        Mockito.reset(projectStatusManager);
        Mockito.reset(projectImageTagMapper);
        listener = new CloneProjectPostProcessingOnProjectCreated(
            projectIIManager,
            projectImageMapper,
            jobScheduler,
            projectStatusManager,
            projectImageTagMapper
        );
    }

    @Test
    void handle_ShouldReturnEarly_WhenCloneTypeIsNotProcessorTraining() {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String cloneFrom = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(
            Message.ProjectMessage.Metadata.newBuilder()
                .setCloneFrom(StringValue.of(cloneFrom))
                .build()
        ); // 不是 PROCESSOR_TRAINING

        // When
        assertDoesNotThrow(() -> listener.handle(event));

        // Then
        verify(projectImageMapper, never()).listAll(anyLong());
    }

    @Test
    void handle_ShouldReturnEarly_WhenSourceProjectHasNoImages() throws Exception {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String cloneFrom = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(
            Message.ProjectMessage.Metadata.newBuilder()
                .setCloneFrom(StringValue.of(cloneFrom))
                .setCloneType(Message.CloneType.PROCESSOR_TRAINING)
                .build()
        );

        when(projectImageMapper.listAll(Long.parseLong(cloneFrom))).thenReturn(Collections.emptyList());

        // When
        assertDoesNotThrow(() -> listener.handle(event));

        // Then
        verify(projectImageMapper, times(1)).listAll(Long.parseLong(cloneFrom));
    }

    @Test
    void handle_ShouldCloneImagesAndScheduleJob_WhenSourceProjectHasImages() throws Exception {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String cloneFrom = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(
            Message.ProjectMessage.Metadata.newBuilder()
                .setCloneFrom(StringValue.of(cloneFrom))
                .setCloneType(Message.CloneType.PROCESSOR_TRAINING)
                .build()
        );

        ProjectImage oldImage = mock(ProjectImage.class);
        ProjectImage clonedImage = mock(ProjectImage.class);
        when(oldImage.clone()).thenReturn(clonedImage);
        when(oldImage.getImageId()).thenReturn("old-image-id");
        when(projectImageMapper.listAll(Long.parseLong(cloneFrom))).thenReturn(List.of(oldImage));

        // When
        listener.handle(event);

        // assert clone project post-processing job
        var countDownLatch = new CountDownLatch(1);
        var jobExecutor =
            new AbstractJobExecutor<ImageCloneJob>() {
                @Override
                public void handle(ImageCloneJob job) {
                    log.info("Received job: {}", job);
                    assertNotNull(job);
                    assertEquals(userId, job.getOperator());
                    assertEquals(1, job.getImages().size());
                    countDownLatch.countDown();
                    log.info("Finished job: {}", job);
                }
            };
        rabbitJobDispatcher.enlist(jobExecutor);
        boolean finished = countDownLatch.await(2, TimeUnit.SECONDS);
        Assertions.assertTrue(finished);

        verify(projectImageMapper, times(1)).listAll(anyLong());
//        verify(projectImageMapper, times(1)).insertBaseInfoList(anyList());
//        verify(projectStatusManager, times(1)).updateStatus(
//            projectId,
//            com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED,
//            userId
//        );
    }

    @Test
    void handle_ShouldThrowException_WhenCloneFromIsEmpty() {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(Message.ProjectMessage.Metadata.newBuilder().setCloneType(Message.CloneType.PROCESSOR_TRAINING).build()); // Empty cloneFrom

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> listener.handle(event));
        assertTrue(exception.getMessage().contains("Clone from project id is blank"));
    }
}
