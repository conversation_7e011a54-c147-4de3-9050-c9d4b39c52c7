package com.bees360.config;

import com.bees360.event.SendCompanyProjectStatsRegularly;
import com.bees360.event.SwyfftProjectAlertRegularly;
import com.bees360.service.MessageService;
import com.bees360.service.statistics.ProjectStatisticsService;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Log4j2
@Configuration
public class ScheduledEventConfig {

    @Bean
    @ConditionalOnProperty(
        prefix = "app.web.scheduled.enabled",
        name = "send-company-project-stats",
        havingValue = "true")
    SendCompanyProjectStatsRegularly sendCompanyProjectStatsRegularly(
        ProjectStatisticsService projectStatisticsService,
        MessageService messageService) {
        return new SendCompanyProjectStatsRegularly(projectStatisticsService, messageService);
    }

    @Bean
    @ConditionalOnProperty(
        prefix = "app.web.scheduled.enabled",
        name = "swyfft-project-alert",
        havingValue = "true")
    SwyfftProjectAlertRegularly swyfftProjectAlertRegularly(MessageService messageService) {
        return new SwyfftProjectAlertRegularly(messageService);
    }
}
