<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-build</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bees360-bom</artifactId>
    <packaging>pom</packaging>

    <!-- version = ${project.parent.version}-->

    <name>bees360-pom</name>

    <dependencyManagement>
        <dependencies>
            <!-- secrets manager start -->
            <dependency>
                <groupId>co.realms9</groupId>
                <artifactId>realms9-secretsmanger-starter</artifactId>
                <version>${realms9-secretmanger-starter.version}</version>
            </dependency>
            <!-- secrets manager end -->
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>api</artifactId>
                <version>${bees360-api.version}</version>
                <exclusions>
                    <!--  using io.grpc version from grpc-bom instead -->
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-netty-shaded</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-stub</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Bees360 Project Module -->
            <dependency>
                <groupId>com.bees360.common</groupId>
                <artifactId>bees360-common</artifactId>
                <version>${bees360.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.ai</groupId>
                <artifactId>bees360-ai</artifactId>
                <version>${bees360.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-web</artifactId>
                <version>${bees360.version}</version>
            </dependency>

            <!-- sub modules ended -->

            <dependency>
                <groupId>com.bees360.commons</groupId>
                <artifactId>spring-support</artifactId>
                <version>${bees360.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.commons</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${bees360.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.commons</groupId>
                <artifactId>elasticsearch-support</artifactId>
                <version>${bees360.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bees360.schedule</groupId>
                <artifactId>bees360-schedule</artifactId>
                <version>${bees360.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bees360.commons</groupId>
                <artifactId>firebase-support</artifactId>
                <version>${bees360.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.commons</groupId>
                <artifactId>hover-support</artifactId>
                <version>${bees360.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.commons</groupId>
                <artifactId>invoice</artifactId>
                <version>${bees360.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-mail-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-mail-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-firebase-event</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-firebase-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-firebase-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-codec</artifactId>
                <version>${utility-kit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-thirdparty-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-project-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-project-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-project-grpc</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-image-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-image-grpc-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-project-http</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-project-grpc-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-todo-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-activity-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-flyzone-http-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-activity-grpc-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-activity-http</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-activity-grpc</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-activity-event</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-flyzone-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-address-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-address-grpc-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-address-event</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-pipeline-grpc-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-pipeline-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-project-event</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-openapi-resource</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-report-api</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-report-grpc-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-report-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-image-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-integration-grpc-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-billing-event</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-rct-http-client</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-rct-auth</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-grpc</artifactId>
                <version>${utility-kit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-project-job</artifactId>
                <version>${bees360-solid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-utility-kit-modules</artifactId>
                <version>${utility-kit.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-bifrost-modules</artifactId>
                <version>${bifrost.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- ============================ Third-party dependencies ============================-->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.auth</groupId>
                <artifactId>google-auth-library-bom</artifactId>
                <version>${google-auth-library.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- gRPC -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-bom</artifactId>
                <version>${grpc-api-pom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- [end] gRPC -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-bom</artifactId>
                <type>pom</type>
                <version>${micrometer-io-version}</version>
                <scope>import</scope>
            </dependency>
            <dependency>
                <artifactId>perfmark-api</artifactId>
                <groupId>io.perfmark</groupId>
                <version>${perfmark-io-version}</version>
            </dependency>
            <!-- firebase -->
            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>${firebase-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>google-cloud-firestore</artifactId>
                <version>${google-cloud-firestore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.api.grpc</groupId>
                <artifactId>proto-google-cloud-firestore-v1</artifactId>
                <version>${google-cloud-firestore.version}</version>
            </dependency>
            <!-- end firebase -->

            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>${lettuce.version}</version>
            </dependency>

            <!-- import:spring-boot-dependencies -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring.framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <!-- <relativePath /> --> <!-- lookup parent from repository -->
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-dependencies -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-security.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework.retry/spring-retry -->
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry-version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ws</groupId>
                <artifactId>spring-ws-security</artifactId>
                <version>${spring-ws-security.version}</version>
            </dependency>
            <dependency>
                <groupId>net.devh</groupId>
                <artifactId>grpc-client-spring-boot-starter</artifactId>
                <version>${spring-grpc-client.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.mybatis.spring.boot/mybatis-spring-boot-starter -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.7.3</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-cdn</artifactId>
                <version>3.8.8</version>
            </dependency>

            <!-- spring boot config file encryption and decryption -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot</artifactId>
                <version>${jasypt-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jasypt</groupId>
                <artifactId>jasypt</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <!-- protocal buffer -->
            <!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java-util -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf-java-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.flatbuffers</groupId>
                <artifactId>flatbuffers-java</artifactId>
                <version>${flatbuffers-java.version}</version>
            </dependency>

            <!-- Tool class dependencies -->
            <!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock-api-mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock-api-mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.github.javafaker</groupId>
                <artifactId>javafaker</artifactId>
                <version>${javafaker.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.yaml</groupId>
                        <artifactId>snakeyaml</artifactId>
                    </exclusion>
                </exclusions>
                <scope>test</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <!-- Mobile Number Toolkit -->
            <dependency>
                <groupId>com.googlecode.libphonenumber</groupId>
                <artifactId>libphonenumber</artifactId>
                <version>8.12.4</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-api -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>txw2</artifactId>
                <version>${txw2.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.hamcrest/hamcrest-all -->
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-client</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-common</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/javax.el/javax.el-api -->
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>3.0.0</version>
            </dependency>

            <!-- thymeleaf template engine -->
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.thymeleaf/thymeleaf-spring5 -->
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf-spring5</artifactId>
                <version>${thymeleaf-spring5.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/ognl/ognl -->
            <!-- The version of ognl used by thymeleaf -->
            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>${ognl.version}</version>
            </dependency>
            <!-- quartz -->
            <!-- https://mvnrepository.com/artifact/org.quartz-scheduler/quartz -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/cglib/cglib -->
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Consider whether it needs to be removed -->
            <!-- https://mvnrepository.com/artifact/jakarta.xml.bind/jakarta.xml.bind-api -->
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jakarta.xml.bind-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.fusesource</groupId>
                <artifactId>sigar</artifactId>
                <version>${sigar.version}</version>
            </dependency>
            <dependency>
                <groupId>jstl</groupId>
                <artifactId>jstl</artifactId>
                <version>${jstl.version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stripe</groupId>
                <artifactId>stripe-java</artifactId>
                <version>${stripe-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.paypal.sdk</groupId>
                <artifactId>rest-api-sdk</artifactId>
                <version>1.14.0</version>
            </dependency>

            <!-- TODO Need to look at other related tool classes to ensure the consistency of versions -->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- TODO According to springboot, add MQ related packages -->

            <!-- https://mvnrepository.com/artifact/org.squirrelframework/squirrel-foundation -->
            <dependency>
                <groupId>org.squirrelframework</groupId>
                <artifactId>squirrel-foundation</artifactId>
                <version>${squirrel-foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.maps</groupId>
                <artifactId>google-maps-services</artifactId>
                <version>${google-maps-services.version}</version>
            </dependency>
            <!-- open street api-->
            <dependency>
                <groupId>fr.dudie</groupId>
                <artifactId>nominatim-api</artifactId>
                <version>${nominatim-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.drewnoakes</groupId>
                <artifactId>metadata-extractor</artifactId>
                <version>${metadata-extractor.version}</version>
            </dependency>

            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnailator.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.im4java</groupId>
                <artifactId>im4java</artifactId>
                <version>${im4java.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.icegreen/greenmail -->
            <dependency>
                <groupId>com.icegreen</groupId>
                <artifactId>greenmail</artifactId>
                <version>${greenmail.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.maxmind.geoip2</groupId>
                <artifactId>geoip2</artifactId>
                <version>2.11.0</version>
            </dependency>
            <!-- Process svg in report start -->
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>xmlgraphics-commons</artifactId>
                <version>${xmlgraphics-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-svggen</artifactId>
                <version>${xmlgraphics.batik.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-xml</artifactId>
                <version>${xmlgraphics.batik.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-rasterizer</artifactId>
                <version>${xmlgraphics.batik.version}</version>
            </dependency>
            <!-- Process svg in the report end -->

            <dependency>
                <groupId>org.apache.santuario</groupId>
                <artifactId>xmlsec</artifactId>
                <version>${santuario.xmlsc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.4</version>
            </dependency>
            <dependency>
                <groupId>javax.jws</groupId>
                <artifactId>javax.jws-api</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.ws</groupId>
                <artifactId>rt</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>7.0</version>
            </dependency>

            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jul</artifactId>
                <version>${log4j2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.embed.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.embed.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.embed.version}</version>
            </dependency>
            <dependency>
                <groupId>org.zalando</groupId>
                <artifactId>logbook-spring-boot-starter</artifactId>
                <version>${zalando.logbook.version}</version>
            </dependency>
            <dependency>
                <groupId>com.vdurmont</groupId>
                <artifactId>emoji-java</artifactId>
                <version>5.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.json</groupId>
                        <artifactId>json</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>${jsonassert.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jmock</groupId>
                <artifactId>jmock</artifactId>
                <version>2.12.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-imaging</artifactId>
                <version>${commons-imaging.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j-slf4j-impl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.oauth-client</groupId>
                <artifactId>google-oauth-client</artifactId>
                <version>${google-oauth-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb-java-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mysql</groupId>
                        <artifactId>mysql-connector-j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${ant.version}</version>
            </dependency>
            <dependency>
                <groupId>xalan</groupId>
                <artifactId>xalan</artifactId>
                <version>${xalan.version}</version>
            </dependency>
            <dependency>
                <groupId>xalan</groupId>
                <artifactId>serializer</artifactId>
                <version>${xalan.version}</version>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>${json-smart.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>${jsch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlbeans</groupId>
                <artifactId>xmlbeans</artifactId>
                <version>${xmlbeans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.vividsolutions</groupId>
                <artifactId>jts-core</artifactId>
                <version>${vividsolutions-jts.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.ws</groupId>
                <artifactId>jaxws-rt</artifactId>
                <version>${jaxws-rt.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <properties>
        <!-- Bees360 Module -->
        <bees360.version>${project.version}</bees360.version>
        <!-- Bees360 solid project module -->
        <bees360-solid.version>3.0.102.1-SNAPSHOT</bees360-solid.version>
        <bifrost.version>2.0.23-RELEASE</bifrost.version>
        <utility-kit.version>2.0.38-RELEASE</utility-kit.version>
        <bees360-api.version>2.0.34-RELEASE</bees360-api.version>
        <realms9-secretmanger-starter.version>1.0.5-RELEASE</realms9-secretmanger-starter.version>
        <!-- spring project version, related imports should be as close to the springboot version as possible -->
        <spring.framework.version>6.2.2</spring.framework.version>
        <spring-boot.version>3.4.2</spring-boot.version>
        <spring-security.version>6.4.4</spring-security.version>
        <spring-cloud.version>3.4.2</spring-cloud.version>
        <!-- defined in springboot dependencies -->
        <jackson-bom.version>2.15.2</jackson-bom.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <spring-retry-version>1.3.1</spring-retry-version>
        <spring-grpc-client.version>3.1.0.RELEASE</spring-grpc-client.version>
        <mybatis-spring-boot-starter.version>3.0.4</mybatis-spring-boot-starter.version>
        <mapper-spring-boot-starter.version>5.0.1</mapper-spring-boot-starter.version>
        <!-- log4j log file management package version -->
        <commons-imaging.version>1.0-alpha2</commons-imaging.version>
        <commons-text.version>1.10.0</commons-text.version>

        <!-- dependencies version -->
        <hamcrest.version>2.2</hamcrest.version>
        <mockito-core.version>5.14.2</mockito-core.version>
        <powermock-api-mockito.version>1.7.4</powermock-api-mockito.version>
        <javafaker.version>1.0.2</javafaker.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <!-- TODO Check where it is used and replace it with collections4 or later versions -->
        <commons-collections.version>20040616</commons-collections.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-pool2.version>2.11.1</commons-pool2.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <commons-io.version>2.18.0</commons-io.version>
        <lombok.version>1.18.36</lombok.version>
        <jsoup.version>1.15.3</jsoup.version>
        <slf4j-api.version>2.0.17</slf4j-api.version>
        <txw2.version>2.4.0-b180830.0438</txw2.version>

        <!-- All components related to protobuf need to be consistent with protobuf-java -->
        <protobuf-java.version>3.25.5</protobuf-java.version>
        <protobuf-java-util.version>3.25.5</protobuf-java-util.version>

        <!-- As a multi-module project, grpc should use a unified version -->
        <grpc-api-pom.version>1.68.2</grpc-api-pom.version>
        <gson.version>2.11.0</gson.version>
        <micrometer-io-version>1.11.0</micrometer-io-version>
        <perfmark-io-version>0.26.0</perfmark-io-version>
        <firebase-admin.version>9.3.0</firebase-admin.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <google-cloud-firestore.version>3.30.8</google-cloud-firestore.version>
        <google-auth-library.version>1.30.1</google-auth-library.version>

        <quartz.version>2.5.0</quartz.version>
        <thymeleaf.version>3.1.2.RELEASE</thymeleaf.version>
        <thymeleaf-spring5.version>3.1.2.RELEASE</thymeleaf-spring5.version>
        <thymeleaf-spring4.version>3.1.2.RELEASE</thymeleaf-spring4.version>
        <ognl.version>3.2.21</ognl.version>

        <cglib.version>3.2.3</cglib.version>
        <mail.version>1.5.0-b01</mail.version>
        <jakarta.inject.version>1</jakarta.inject.version>

        <easyexcel.version>4.0.3</easyexcel.version>
        <!-- Because the version used by com.alibaba:easyexcel:2.1.2 is 3.17, and only this version can be used here to keep the program normal -->
        <poi.version>5.2.5</poi.version>

        <greenmail.version>2.1.3</greenmail.version>

        <jasypt-spring-boot.version>3.0.5</jasypt-spring-boot.version>
        <jasypt.version>1.9.3</jasypt.version>
        <fastjson.version>1.2.83</fastjson.version>
        <mail.version>1.5.0-b01</mail.version>
        <sigar.version>1.6.4</sigar.version>
        <jstl.version>1.2</jstl.version>
        <joda-time.version>2.8.1</joda-time.version>
        <stripe-java.version>7.25.0</stripe-java.version>
        <aspectjweaver.version>1.9.0</aspectjweaver.version>
        <squirrel-foundation.version>0.3.8</squirrel-foundation.version>
        <google-maps-services.version>0.9.0</google-maps-services.version>
        <nominatim-api.version>3.3</nominatim-api.version>
        <metadata-extractor.version>2.18.0</metadata-extractor.version>
        <thumbnailator.version>0.4.8</thumbnailator.version>
        <jjwt.version>0.9.0</jjwt.version>
        <im4java.version>1.4.0</im4java.version>
        <elasticsearch-rest.version>7.6.1</elasticsearch-rest.version>
        <flatbuffers-java.version>1.8.0</flatbuffers-java.version>
        <log4j2.version>2.24.3</log4j2.version>
        <tomcat.embed.version>9.0.65</tomcat.embed.version>
        <zalando.logbook.version>3.11.0</zalando.logbook.version>
        <jsonassert.version>1.5.0</jsonassert.version>
        <log4j-slf4j-impl.version>2.24.2</log4j-slf4j-impl.version>
        <google-oauth-client.version>1.33.3</google-oauth-client.version>
        <mariadb-java-client.version>3.5.1</mariadb-java-client.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <ant.version>1.9.12</ant.version>
        <santuario.xmlsc.version>2.2.6</santuario.xmlsc.version>
        <xalan.version>2.7.3</xalan.version>
        <xmlgraphics-commons.version>2.6</xmlgraphics-commons.version>
        <bcprov-jdk18on.version>1.79</bcprov-jdk18on.version>
        <json-smart.version>2.5.2</json-smart.version>
        <xmlgraphics.batik.version>1.18</xmlgraphics.batik.version>
        <jsch.version>0.1.55</jsch.version>
        <netty-bom.version>4.1.118.Final</netty-bom.version>
        <xmlbeans.version>5.2.0</xmlbeans.version>
        <vividsolutions-jts.version>1.14.0</vividsolutions-jts.version>
        <okhttp.version>4.12.0</okhttp.version>
        <okio.version>3.6.0</okio.version>
        <kotlin-stdlib.version>1.8.21</kotlin-stdlib.version>
        <lettuce.version>6.5.4.RELEASE</lettuce.version>
        <json-path.version>2.9.0</json-path.version>
        <spring-ws-security.version>4.0.12</spring-ws-security.version>
        <commons-net.version>3.9.0</commons-net.version>
        <jakarta.xml.bind-api.version>4.0.2</jakarta.xml.bind-api.version>
        <jaxws-rt.version>4.0.3</jaxws-rt.version>
    </properties>

</project>
