package com.bees360.event;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.event.registry.CronTriggerDailyAt9AmEveryMondayCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.MessageService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * 定期发送Swyfft项目警报，每周一上午9点触发，通过消息服务发送上周一至上周日的项目警报。
 */
@Log4j2
public class SwyfftProjectAlertRegularly extends AbstractNamedEventListener<CronTriggerDailyAt9AmEveryMondayCst> {

    private final MessageService messageService;

    private static final ZoneId zoneID = ZoneId.of(AmericaTimeZone.US_CENTRAL);

    public SwyfftProjectAlertRegularly(@NonNull MessageService messageService) {
        this.messageService = messageService;
        log.info("Created {}(messageService={})", this, messageService);
    }

    @Override
    public void handle(CronTriggerDailyAt9AmEveryMondayCst event) throws IOException {
        ZonedDateTime now = ZonedDateTime.now(zoneID);
        LocalDate today = now.toLocalDate();
        LocalDate yesterday = today.minusDays(1);
        LocalDate lastMonday = today.minusDays(7);
        log.info("Send swyfft project alert by event {}", event);
        messageService.sendProjectAlerts(lastMonday.atStartOfDay(zoneID), yesterday.atTime(LocalTime.MAX).atZone(zoneID));
    }
}
