<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectImageMapper">

	<resultMap id="baseProjectImageMap" type="com.bees360.entity.ProjectImage">
	    <id column="image_id" jdbcType="VARCHAR" property="imageId"/>
	    <result column="project_id" jdbcType="INTEGER" property="projectId" />
	    <result column="user_id" jdbcType="BIGINT" property="userId" />
	    <result column="original_file_name" jdbcType="VARCHAR" property="originalFileName" />
	    <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
	    <result column="file_name_middle_resolution" jdbcType="VARCHAR" property="fileNameMiddleResolution"/>
	    <result column="file_name_lower_resolution" jdbcType="VARCHAR" property="fileNameLowerResolution"/>
        <result column="annotation_image" jdbcType="VARCHAR" property="annotationImage" />
	    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
	    <result column="upload_time" jdbcType="BIGINT" property="uploadTime" />
	    <result column="file_source_type" jdbcType="INTEGER" property="fileSourceType" />
	    <result column="gps_location_longitude" jdbcType="DOUBLE" property="gpsLocationLongitude" />
		<result column="gps_location_latitude" jdbcType="DOUBLE" property="gpsLocationLatitude" />
		<result column="relative_altitude" jdbcType="DOUBLE" property="relativeAltitude" />
	    <result column="image_height" jdbcType="INTEGER" property="imageHeight" />
		<result column="image_width" jdbcType="INTEGER" property="imageWidth" />
        <result column="direction" jdbcType="INTEGER" property="direction" />
        <result column="orientation" jdbcType="INTEGER" property="orientation" />
        <result column="image_type" jdbcType="INTEGER" property="imageType" />
        <result column="image_category" jdbcType="VARCHAR" property="imageCategory" />
        <result column="cam_property_matrix" jdbcType="VARCHAR" property="camPropertyMatrix" />
		<result column="is_deleted" jdbcType="INTEGER" property="deleted" />
		<result column="manually_annotated" jdbcType="INTEGER" property="manuallyAnnotated" />
		<result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
		<result column="weight" jdbcType="DOUBLE" property="weight"/>
		<result column="tiff_orientation" jdbcType="INTEGER" property="tiffOrientation"/>
		<result column="shooting_time" jdbcType="BIGINT" property="shootingTime"/>
        <result column="partial_type" jdbcType="INTEGER" property="partialType"/>
        <result column="image_sort" jdbcType="BIGINT" property="imageSort"/>
        <result column="in_3d_model" jdbcType="INTEGER" property="in3DModel"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="room_name" jdbcType="VARCHAR" property="roomName"/>
        <result column="tag_category" jdbcType="VARCHAR" property="tagCategory"/>
        <result column="tag_description" jdbcType="VARCHAR" property="tagDescription"/>
        <result column="floor_level" jdbcType="VARCHAR" property="floorLevel"/>
        <result column="number" jdbcType="VARCHAR" property="number"/>
	</resultMap>

    <resultMap id="projectImageReportTypeMap" type="com.bees360.entity.vo.ProjectImageAnnotationVo" extends="baseProjectImageMap">
        <result column="report_type" jdbcType="INTEGER" property="reportType"/>
        <result column="caption" jdbcType="VARCHAR" property="caption"/>
        <result column="alias" jdbcType="VARCHAR" property="alias"/>
    </resultMap>

	<!-- without cam_property_matrix -->
	<sql id="projectImageColumns">
		image_id, project_id, user_id, original_file_name, file_name, file_name_lower_resolution,
		file_name_middle_resolution, annotation_image, file_size, upload_time, file_source_type,
        ST_X(gps_location) as gps_location_longitude,ST_Y(gps_location) as gps_location_latitude,
        relative_altitude, image_height, image_width, direction, orientation, image_type, image_category,
        is_deleted, manually_annotated, parent_id, tiff_orientation, shooting_time, partial_type, image_sort,
        in_3d_model, room_name, update_time, tag_category, tag_description, floor_level, number
	</sql>

	<!-- with cam_property_matrix -->
    <sql id="projectImageFullColumns">
        image_id, project_id, user_id, original_file_name, file_name, file_name_lower_resolution,
        file_name_middle_resolution, annotation_image, file_size, upload_time, file_source_type, cam_property_matrix,
        ST_X(gps_location) as gps_location_longitude,ST_Y(gps_location) as gps_location_latitude, relative_altitude,
        image_height, image_width, direction, orientation, image_type, image_category, is_deleted,
        manually_annotated, parent_id, tiff_orientation, shooting_time, partial_type, in_3d_model, update_time
    </sql>

    <sql id="projectImageBaseAliasNameColumns">
		pi.image_id, pi.project_id, pi.user_id, pi.original_file_name, pi.file_name, pi.file_name_lower_resolution,
		pi.file_name_middle_resolution, pi.annotation_image, pi.file_size, pi.upload_time, pi.file_source_type,
        ST_X(pi.gps_location) as gps_location_longitude, ST_Y(pi.gps_location) as gps_location_latitude,
        pi.cam_property_matrix, pi.image_height, pi.image_width, pi.direction, pi.shooting_time, pi.image_category,
        pi.image_type, pi.parent_id, pi.orientation, pi.in_3d_model, pi.image_sort, pi.room_name, pi.tag_category,
        pi.tag_description, pi.number, pi.floor_level
    </sql>

    <sql id="projectImageFullAliasNameColumns">
        <include refid="projectImageBaseAliasNameColumns" />,
        pi.relative_altitude, pi.is_deleted, pi.manually_annotated, pi.tiff_orientation, pi.partial_type
    </sql>

    <sql id="projectImageColumnsWithTagColumn">
        P.image_id, P.project_id, P.user_id, P.original_file_name, P.file_name, P.file_name_lower_resolution,
        P.file_name_middle_resolution, P.annotation_image, P.file_size, P.upload_time, P.file_source_type,
        ST_X(P.gps_location) as gps_location_longitude,ST_Y(P.gps_location) as gps_location_latitude,
        P.relative_altitude, P.image_height, P.image_width, P.direction, P.orientation, P.image_type,
        P.is_deleted, P.manually_annotated, P.parent_id, P.tiff_orientation, P.shooting_time, P.partial_type, P.image_sort,
        P.in_3d_model, P.room_name, P.update_time, P.tag_category, P.tag_description, P.floor_level, P.number,
        PIT.image_category
    </sql>

	<!-- image file source type -->
	<sql id="screenshotType">2</sql>

    <sql id="fileSourceTypeAnnotationImage">6</sql>

    <!-- Completely delete image -->
    <sql id="completelyDelete">-1</sql>
    <sql id="notCompletelyDelete"> and is_deleted <![CDATA[<>]]> <include refid="completelyDelete" /></sql>
    <sql id="notCompletelyDeleteByPi"> and pi.is_deleted <![CDATA[<>]]> <include refid="completelyDelete" /></sql>

	<sql id="imageUpdateEntity">
			 file_name = #{image.fileName},
  	         file_name_middle_resolution = #{image.fileNameMiddleResolution},
  	         file_name_lower_resolution = #{image.fileNameLowerResolution},
  	         image_height = #{image.imageHeight},
  	         image_width = #{image.imageWidth},
  	         gps_location = Point(#{image.gpsLocationLongitude},#{image.gpsLocationLatitude}),
  	         relative_altitude = #{image.relativeAltitude},
  	         file_source_type = #{image.fileSourceType},
  	         shooting_time = #{image.shootingTime},
  	         update_time = <include refid="timeStamp" />
  	</sql>
    <sql id="timeStamp">
        REPLACE(unix_timestamp(current_timestamp(3)),'.','')
    </sql>

	<!-- select -->
	<select id="getById" resultMap="baseProjectImageMap">
	    select <include refid="projectImageColumns" />
        from ProjectImage
        where project_id = #{projectId} and image_id = #{imageId} <include refid="notCompletelyDelete" />
	</select>

	<select id="listByPartialType" resultMap="baseProjectImageMap">
	    select <include refid="projectImageColumns" />
        from ProjectImage
        where project_id = #{projectId} and partial_type = #{partialType} and is_deleted = 0
	</select>

    <select id="getImageById" resultMap="baseProjectImageMap">
        select <include refid="projectImageColumns" /> from ProjectImage
        where image_id = #{imageId} <include refid="notCompletelyDelete" />
    </select>

	<select id="listImagesByProjectIdExcludePlaceholder" resultMap="baseProjectImageMap">
		select <include refid="projectImageColumns" />
		from ProjectImage
		where project_id = #{projectId} and file_source_type != 3 and is_deleted = 0 order by original_file_name;
	</select>

    <select id="listOriginalFileNameIncludeCompleteDeleted" resultType="java.lang.String">
        select original_file_name from ProjectImage
        where project_id = #{projectId}
        and image_id in
        <foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="fileSourceTypes != null and fileSourceTypes.size() > 0">
            and file_source_type in
            <foreach collection="fileSourceTypes" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

	<select id="listAll" resultMap="baseProjectImageMap">
		select <include refid="projectImageColumns" />
		from ProjectImage
		where project_id = #{projectId} and is_deleted = 0 order by original_file_name;
	</select>

    <select id="listAllContainDeleted" resultMap="baseProjectImageMap">
		select <include refid="projectImageColumns" />
		from ProjectImage
		where project_id = #{projectId} <include refid="notCompletelyDelete" />
        order by original_file_name;
	</select>

	<select id="listImages" resultMap="baseProjectImageMap">
		select <include refid="projectImageColumnsWithTagColumn" />
		from ProjectImage P
        left join ProjectImageTag PIT on P.image_id = PIT.image_id
		where project_id = #{projectId} and file_source_type = #{fileSourceType} and is_deleted = 0 order by original_file_name;
	</select>

    <sql id="listImagesPageWhere">
        <where>
            <if test="projectId != null">
                pi.project_id = #{projectId}
            </if>
            <include refid="notCompletelyDeleteByPi" />
            <if test="fileSourceType != null">
                and pi.file_source_type = #{fileSourceType}
            </if>
            <if test="imageType != null">
                <choose>
                    <when test="imageType == 0">
                        and (pi.image_type is null or pi.image_type = 0)
                    </when>
                    <otherwise>
                        and pi.image_type = #{imageType}
                    </otherwise>
                </choose>
            </if>
            <if test="partialType != null">
                and pi.partial_type = #{partialType}
            </if>
            <if test="excludeFileSourceType != null">
                and pi.file_source_type != #{excludeFileSourceType}
            </if>
            <if test="deleted != null">
                and pi.is_deleted = #{deleted}
            </if>
            <if test="fileSourceTypes != null and fileSourceTypes.size() > 0">
                and pi.file_source_type in
                <foreach collection="fileSourceTypes" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="projectIdList != null and projectIdList.size() > 0">
                and pi.project_id in
                <foreach collection="projectIdList" item="projectId" separator="," open="(" close=")">
                    #{projectId}
                </foreach>
            </if>
            <if test="needReportType != null and needReportType and reportType != null">
                and ram.report_type = #{reportType}
            </if>
        </where>
    </sql>
    <select id="listImagesPage" resultMap="projectImageReportTypeMap"
        parameterType="com.bees360.entity.dto.ProjectImageSearchOptionDto">
        select <include refid="projectImageFullAliasNameColumns"/>
        <if test="needReportType != null and needReportType">
            , ram.report_type as report_type, ram.caption as caption, ram.alias as alias
        </if>
        from ProjectImage pi
        <if test="needReportType != null and needReportType">
            LEFT JOIN ReportAnnotationImage ram ON ram.image_id = pi.image_id AND ram.is_deleted = 0
        </if>
        <include refid="listImagesPageWhere"/>
        <choose>
            <!-- 0 file_source_type is drone image type. -->
            <when test="fileSourceType != null and fileSourceType == 0">
                order by pi.original_file_name asc
            </when>
            <otherwise>
                order by pi.upload_time asc
            </otherwise>
        </choose>
        <if test="startIndex != null">
            limit #{startIndex}, #{pageSize};
        </if>
    </select>

    <select id="countImagesPageTotal" resultType="int"
        parameterType="com.bees360.entity.dto.ProjectImageSearchOptionDto">
        select count(*) from ProjectImage pi
        <include refid="listImagesPageWhere"/>
    </select>

    <select id="countImages" resultType="int">
	    select count(*) from ProjectImage
	        where project_id = #{projectId} <include refid="notCompletelyDelete" />
               <if test="fileSourceType != null">
                   and file_source_type = #{fileSourceType}
               </if>
               <if test="imageType != null">
	                <choose>
	                    <when test="imageType == 0">
	                        and (image_type is null or image_type = 0)
	                    </when>
	                    <otherwise>
	                        and image_type = #{imageType}
	                    </otherwise>
	                </choose>
	            </if>
	           <if test="deleted != null" >
                    and is_deleted = #{deleted}
	           </if>
    </select>

	<select id="countImagesForMultiTypes" resultType="java.lang.Integer">
		select count(*) from ProjectImage
		where project_id = #{projectId} <include refid="notCompletelyDelete" />
		<if test="fileSourceTypes != null">
			and file_source_type in
		  	<foreach collection="fileSourceTypes" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="imageTypes != null">
			and image_type in
			<foreach collection="imageTypes" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="deleted != null" >
			and is_deleted = #{deleted}
		</if>
	</select>

    <select id="listImageByImageType" resultMap="baseProjectImageMap">
        select <include refid="projectImageColumns" />
	        from ProjectImage
	        where project_id = #{projectId} and file_source_type = #{fileSourceType}
	           and image_type = #{imageType} and is_deleted = 0
	        order by original_file_name asc;
    </select>

	<select id="listImageFileSourceTypes" resultType="com.bees360.entity.dto.IdTypeDto">
		select pi.file_source_type type
        from ProjectImage pi
		where pi.project_id=#{projectId} and pi.is_deleted=0
		group by pi.file_source_type
		order by pi.file_source_type asc;
	</select>

    <select id="listScreenshotIds" resultType="java.lang.String">
        select image_id from ProjectImage
            where project_id = #{projectId} and file_source_type = <include refid="screenshotType" />
                and parent_id = #{imageId} and is_deleted = 0
    </select>

    <select id="listScreenshotIdsIn" resultType="java.lang.String">
       select image_id from ProjectImage
           where project_id = #{projectId} and file_source_type = <include refid="screenshotType" />
               and is_deleted = 0 and parent_id in
             <foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
             </foreach>
    </select>

    <select id="listFrontElevationByProjectIds" resultMap="baseProjectImageMap">
        select <include refid="projectImageColumns" /> from ProjectImage PI
            where PI.file_source_type = #{fileSourceType} and PI.is_deleted = 0
                and PI.orientation = #{orientation} and PI.image_type = #{imageType}
                and PI.project_id in
	            <foreach collection="projectIds" item="projectId" index="index" open="(" close=")" separator=",">
		            #{projectId}
		        </foreach>
                ;
    </select>

	<select id="getImageByProjectId" resultMap="baseProjectImageMap">
		SELECT * FROM ProjectImage WHERE project_id = #{projectId} <include refid="notCompletelyDelete" />
	</select>

    <select id="listByImageTypeAndFileSourceType" resultMap="baseProjectImageMap">
    	select <include refid="projectImageColumns" /> from ProjectImage
    	where image_type = #{imageType}
    		and file_source_type = #{fileSourceType}
			and is_deleted = #{isDeleted}
			and project_id = #{projectId}
    	order by upload_time desc;
    </select>

  	<insert id="insertBaseInfoList" useGeneratedKeys="true" keyProperty="imageId" parameterType="java.util.List">
	   insert into ProjectImage (image_id, project_id, user_id, original_file_name, file_name, file_name_middle_resolution, file_name_lower_resolution,
                                file_size, upload_time, file_source_type, gps_location, relative_altitude, partial_type,
                                image_height, image_width, is_deleted, parent_id, orientation, image_type, weight, tiff_orientation, shooting_time,
	                             image_sort, room_name, tag_category, tag_description, floor_level , number, update_time)
        values
	   <foreach collection="list" item="item" separator=",">
		    (#{item.imageId}, #{item.projectId}, #{item.userId}, #{item.originalFileName}, #{item.fileName}, #{item.fileNameMiddleResolution}, #{item.fileNameLowerResolution},
	        #{item.fileSize}, #{item.uploadTime}, #{item.fileSourceType}, Point(#{item.gpsLocationLongitude}, #{item.gpsLocationLatitude}), #{item.relativeAltitude}, #{item.partialType},
            #{item.imageHeight}, #{item.imageWidth}, #{item.deleted}, #{item.parentId}, #{item.orientation}, #{item.imageType}, #{item.weight}, #{item.tiffOrientation},
		     #{item.shootingTime}, #{item.imageSort}, #{item.roomName}, #{item.tagCategory}, #{item.tagDescription}, #{item.floorLevel}, #{item.number}, <include refid="timeStamp" />)
	   </foreach>
	</insert>

	<!-- update -->
  	<update id="deleteById">
  		update ProjectImage set is_deleted = 1, update_time = <include refid="timeStamp" />
  		where project_id=#{projectId} and image_id = #{imageId};
  	</update>

  	<update id="deleteImages">
  		update ProjectImage set is_deleted = 1, update_time = <include refid="timeStamp" />
  		where project_id=#{projectId} and image_id in
		<foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
  	</update>

    <update id="deleteCompletely">
        update ProjectImage set is_deleted = <include refid="completelyDelete" />,
        update_time = <include refid="timeStamp" />
        where project_id=#{projectId} and image_id in
        <foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

  	<update id="updateImageDeleted">
        update ProjectImage set is_deleted = #{deleted}, update_time = <include refid="timeStamp" />
        where project_id=#{projectId} and image_id in
        <foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateImageType">
  	    update ProjectImage
            <set>
              image_type = #{imageType}, update_time = <include refid="timeStamp" />
            </set>
            where project_id = #{projectId} and image_id = #{imageId};
  	</update>

  	<update id="deleteAllScreenshotsOf">
         update ProjectImage set is_deleted = 1, update_time = <include refid="timeStamp" />
             where project_id = #{projectId} and file_source_type = <include refid="screenshotType" />
             <if test="fileSourceType != null">
                and parent_id in
                <foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
             </if>
    </update>

  	<update id="deleteScreenshotIn">
  	     update ProjectImage set is_deleted = 1, update_time = <include refid="timeStamp" />
  	         where project_id = #{projectId} and file_source_type = <include refid="screenshotType" />
  	         and parent_id in
                <foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
  	</update>

  	<update id="deleteScreenshotForImage">
  	     update ProjectImage set is_deleted = 1, update_time = <include refid="timeStamp" />
             where project_id = #{projectId} and file_source_type = <include refid="screenshotType" />
                 and parent_id = #{imageId}
  	</update>

    <update id="deleteByReportTypesAndImageIds">
        update ProjectImage pi left join ReportAnnotationImage rai on pi.image_id = rai.image_id and rai.is_deleted = 0
        set pi.is_deleted = <include refid="completelyDelete" />, pi.update_time = <include refid="timeStamp" />
        where pi.project_id = #{projectId} and pi.is_deleted = 0 and pi.file_source_type = <include refid="fileSourceTypeAnnotationImage"/>
        and rai.report_type in
        <foreach collection="reportTypes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="imageIds != null and imageIds.size() > 0">
            and pi.image_id in
            <foreach collection="imageIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateImageProperty">
        update ProjectImage set
                                is_deleted = #{image.deleted},
                                orientation = #{image.orientation},
                                image_type = #{image.imageType},
                                partial_type = #{image.partialType},
                                file_source_type = #{image.fileSourceType},
                                room_name = #{image.roomName},
                                floor_level = #{image.floorLevel},
                                number = #{image.number},
                                update_time = <include refid="timeStamp" />
        where project_id = #{projectId} and image_id = #{image.imageId}
    </update>

    <update id="updateReportRelevantData" parameterType="java.util.List">
        update ProjectImage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="annotation_image=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    <if test="item.annotationImage!=null">
                        when image_id=#{item.imageId} then #{item.annotationImage}
                    </if>
                </foreach>
            </trim>
            <trim prefix="direction=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.direction}
                </foreach>
            </trim>
            <trim prefix="orientation=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.orientation}
                </foreach>
            </trim>
            <trim prefix="partial_type=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.partialType}
                </foreach>
            </trim>
            <trim prefix="image_type=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.imageType}
                </foreach>
            </trim>
            <trim prefix="cam_property_matrix=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    <if test="item.camPropertyMatrix!=null">
                        when image_id=#{item.imageId} then #{item.camPropertyMatrix}
                    </if>
                </foreach>
            </trim>
            <trim prefix="image_sort=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.imageSort}
                </foreach>
            </trim>
            <trim prefix="file_source_type=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.fileSourceType}
                </foreach>
            </trim>
            <trim prefix="in_3d_model=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.in3DModel}
                </foreach>
            </trim>
            <trim prefix="tag_category=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.tagCategory}
                </foreach>
            </trim>
            <trim prefix="tag_description=case" suffix="end,">
                <foreach collection="imageList" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.tagDescription}
                </foreach>
            </trim>
            is_deleted = 0, update_time = <include refid="timeStamp" />
        </trim>
        where project_id = #{projectId} and image_id in
        <foreach collection="imageList" index="index" item="item" separator="," open="(" close=")">
            #{item.imageId}
        </foreach>
    </update>

    <select id="existByPartialType" resultType="java.lang.Boolean">
        select exists(select image_id from ProjectImage where project_id = #{projectId} and
        partial_type in
        <foreach collection="partialTypes" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        );
    </select>
</mapper>
