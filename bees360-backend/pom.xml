<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bees360</groupId>
    <artifactId>bees360-build</artifactId>
    <version>${revision}${changelist}</version>
    <packaging>pom</packaging>

    <name>bees360-build</name>
  <dependencies>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-junit</artifactId>
      <version>2.0.0.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

    <!-- 该parent模块不做聚合作用，不需要添加 modules -->
    <modules>
        <module>common</module>
        <module>commons</module>
        <module>ai</module>
        <module>web</module>
        <module>bom</module>
        <module>report</module>
        <module>parent</module>
        <module>schedule</module>
        <module>test-report-aggregate</module>
    </modules>

    <properties>
        <!-- maven 项目基础属性设置 -->
        <changelist>-LOCAL</changelist>
        <java.resource>17</java.resource>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.testTarget>17</maven.compiler.testTarget>
        <maven.compiler.testSource>17</maven.compiler.testSource>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <profile.docker.registry>bees360</profile.docker.registry>
        <maven-repository.url>http://nexus.9realms.co/repository</maven-repository.url>

        <spring-boot-maven-plugin.version>3.3.10</spring-boot-maven-plugin.version>
        <git-commit-id-plugin.version>4.0.0</git-commit-id-plugin.version>
        <skipJib>false</skipJib>
        <skipNexusDeploy>false</skipNexusDeploy>
        <!-- Maven -->
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-failsafe-plugin.version>3.5.2</maven-failsafe-plugin.version>
        <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
        <maven-javadoc-plugin.version>3.2.0</maven-javadoc-plugin.version>
        <maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.12</jacoco-maven-plugin.version>
        <jib-maven-plugin.version>2.8.0</jib-maven-plugin.version>
        <os-maven-plugin.version>1.6.2</os-maven-plugin.version>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <maven-repository.snapshot-enabled>false</maven-repository.snapshot-enabled>
    </properties>

    <repositories>
        <repository>
            <id>bees360-maven-repo</id>
            <url>http://nexus.9realms.co/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <profiles>
        <profile>
            <id>ci</id>
            <properties>
                <!--suppress UnresolvedMavenProperty -->
                <profile.docker.registry>harbor.9realms.co/upstream</profile.docker.registry>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>flatten-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>pl.project13.maven</groupId>
                        <artifactId>git-commit-id-plugin</artifactId>
                    </plugin>
                </plugins>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>com.google.cloud.tools</groupId>
                            <artifactId>jib-maven-plugin</artifactId>
                            <configuration>
                                <to>
                                    <tags>
                                        <tag>latest</tag>
                                        <!--suppress UnresolvedMavenProperty -->
                                        <tag>git-${git.commit.id}</tag>
                                    </tags>
                                </to>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>master</id>
            <distributionManagement>
                <repository>
                    <id>bees360-maven-repo</id>
                    <url>${maven-repository.url}/maven-snapshots</url>
                </repository>
            </distributionManagement>
        </profile>
        <profile>
            <id>release</id>
            <repositories>
                <repository>
                    <id>bees360-maven-repo</id>
                    <url>${maven-repository.url}/maven-public</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>never</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>${maven-repository.snapshot-enabled}</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>bees360-maven-repo</id>
                    <url>${maven-repository.url}/maven-releases</url>
                </repository>
            </distributionManagement>
        </profile>
        <profile>
            <id>sonar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!-- Optional URL to server. Default value is http://localhost:9000 -->
                <sonar.host.url>
                  https://sonar.bees360.com
                </sonar.host.url>
            </properties>
        </profile>
    </profiles>

    <!-- 编译使用的插件 -->
    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.14.0</version>
                    <configuration>
                        <parameters>true</parameters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>kr.motd.maven</groupId>
                    <artifactId>os-maven-plugin</artifactId>
                    <version>${os-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                    <configuration>
                        <skip>${skipNexusDeploy}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>test-jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <trimStackTrace>false</trimStackTrace>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                        <excludes>
                            <exclude>**/*IntegrationTest.java</exclude>
                            <exclude>**/*ITest.java</exclude>
                        </excludes>
                        <forkCount>1</forkCount>
                        <runOrder>random</runOrder>
                        <skipAfterFailureCount>1</skipAfterFailureCount>
                        <argLine>--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.regex=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED</argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                    <configuration>
                        <trimStackTrace>false</trimStackTrace>
                        <includes>
                            <include>**/*IntegrationTest.java</include>
                            <include>**/*ITest.java</include>
                        </includes>
                        <excludes>
                            <exclude>**/*UnitTest.java</exclude>
                        </excludes>
                        <argLine>--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.regex=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED</argLine>
                        <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>integration-test</phase>
                            <goals>
                                <goal>integration-test</goal>
                                <goal>verify</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>get-git-infos</id>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                            <phase>validate</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <dateFormat>yyyyMMdd</dateFormat>
                        <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
                        <generateGitPropertiesFile>false</generateGitPropertiesFile>
                        <offline>true</offline>
                        <skipPoms>false</skipPoms>
                        <includeOnlyProperties>
                            <includeOnlyProperty>git.branch</includeOnlyProperty>
                            <includeOnlyProperty>git.commit.time</includeOnlyProperty>
                            <includeOnlyProperty>git.commit.id</includeOnlyProperty>
                            <includeOnlyProperty>git.commit.id.abbrev</includeOnlyProperty>
                        </includeOnlyProperties>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <layers>
                            <enabled>true</enabled>
                        </layers>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>${jib-maven-plugin.version}</version>
                    <configuration>
                        <skip>${skipJib}</skip>
                        <to>
                            <image>${profile.docker.registry}/${project.artifactId}:${project.version}</image>
                            <tags>
                                <tag>latest</tag>
                            </tags>
                        </to>
                    </configuration>
                    <executions>
                        <execution>
                            <id>build-and-push-to-registry</id>
                            <phase>deploy</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>prepare-agent-integration</id>
                            <goals>
                                <goal>prepare-agent-integration</goal>
                            </goals>
                            <configuration>
                                <propertyName>failsafe.jacoco.args</propertyName>
                            </configuration>
                        </execution>
                        <execution>
                            <id>report</id>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report-integration</id>
                            <goals>
                                <goal>report-integration</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <excludes>
                            <exclude>**/*Properties.*</exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
                <reportSets>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                    <reportSet>
                        <id>default</id>
                        <reports>
                            <report>javadoc</report>
                        </reports>
                    </reportSet>
                </reportSets>
                <configuration>
                    <failOnWarnings>true</failOnWarnings>
                    <quiet>true</quiet>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>report</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>
</project>
