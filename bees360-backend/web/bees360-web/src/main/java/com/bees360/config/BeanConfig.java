package com.bees360.config;

import com.bees360.atomic.LockProvider;
import com.bees360.atomic.RedisLockProvider;
import com.bees360.config.project.ParentChildProjectConfig;
import com.bees360.entity.Project;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.http.ApacheHttpClient;
import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.mapper.ProjectCreationMapper;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.resource.HttpResourceClient;
import com.bees360.resource.ResourceContext;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.config.GrpcResourceClientConfig;
import com.bees360.service.file.ResourceKeyUtil;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360CompanyConfig.CompanyConfigItem;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.file.CompositeSizeReduce;
import com.bees360.util.file.SizeReduce;
import com.bees360.util.pdf.gs.GsPdfSizeReducer;
import java.time.Duration;
import java.util.Optional;
import java.util.function.Predicate;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

import java.net.URI;

@Log4j2
@Configuration
@Import({
    ApacheHttpClientConfig.class,
    GrpcResourceClientConfig.class,
    ParentChildProjectConfig.class
})
public class BeanConfig {

    private final String MISSION_LOCK = "mission_lock";

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Bean
    public ResourcePool resourcePool(GrpcResourceClient grpcResourceClient) {
        // 用GrpcResourceClient当作默认的ResourcePool
        return grpcResourceClient;
    }

    @Bean
    public ResourceUrlProvider resourceUrlProvider(HttpResourceClient httpResourceClient) {
        return httpResourceClient.asResourceUrlProvider();
    }

    @Bean("httpClient")
    public ApacheHttpClient httpClient(ApacheHttpClient apacheHttpClient) {
        return apacheHttpClient;
    }

    @Bean
    public HttpResourceClient httpResourceClient(@Value("${resource.client.base-url}") URI resourceServer, HttpClient httpClient) {
        return new HttpResourceClient(resourceServer, httpClient);
    }

    /**
     * Pdf压缩
     */
    @Bean
    public GsPdfSizeReducer pdfSizeReducer() {
        return new GsPdfSizeReducer();
    }

    @Bean
    @Primary
    public SizeReduce sizeReduce() {
        CompositeSizeReduce sizeReduce = new CompositeSizeReduce();
        SizeReduce pdfSizeReduce = pdfSizeReducer();
        sizeReduce.add("pdf", pdfSizeReduce);
        return sizeReduce;
    }

    @Bean
    public ResourceKeyUtil reportKeyUtil(ResourceContext resourceContext) {
        return new ResourceKeyUtil(resourceContext);
    }

    @Bean
    public ResourceContext resourceContext(@Value("${resource.client.base-url}") URI resourceServer) {
        return ResourceContext.of(resourceServer);
    }

    @Bean
    public LockProvider missionLockProvider(RedissonClient redissonClient) {
        return new RedisLockProvider(redissonClient, MISSION_LOCK, Duration.ofMinutes(1));
    }

    @Bean
    public Predicate<Project> projectAutoClientReceivedPredicate(
            ProjectCreationMapper projectCreationMapper,
            Bees360CompanyConfig bees360CompanyConfig,
            Bees360FeatureSwitch bees360FeatureSwitch,
            @Qualifier("parentChildProjectPredicate") Predicate<String> parentChildProjectPredicate) {
        return project -> {

            // not claim project
            boolean autoClientReceived = Optional.ofNullable(project).map(Project::getServiceType)
                .map(t -> !ProjectServiceTypeEnum.isClaim(t)).orElse(false);

            // not a parent-child project
            if (bees360FeatureSwitch.isEnableParentChildProject()) {
                autoClientReceived = autoClientReceived
                        && !parentChildProjectPredicate.test(String.valueOf(project.getProjectId()));
            }

            // is specified service types
            autoClientReceived = autoClientReceived && bees360CompanyConfig
                .getGlobal()
                .getAutoClientReceivedConfig()
                .isServiceTypeHit(project.getServiceType());

            // check config in company-config
            autoClientReceived = autoClientReceived && Optional.ofNullable(project.getInsuranceCompany())
                .map(bees360CompanyConfig::findConfig).map(CompanyConfigItem::getAutoClientReceived)
                .orElse(bees360CompanyConfig.getGlobal().isAutoClientReceived());

            // not from open api
            autoClientReceived =
                autoClientReceived && Optional.ofNullable(projectCreationMapper.getByProjectId(project.getProjectId()))
                    .map(c -> !CreationChannelType.OPENAPI.name().equals(c.getCreationChannel())).orElse(true);

            return autoClientReceived;
        };
    }
}
