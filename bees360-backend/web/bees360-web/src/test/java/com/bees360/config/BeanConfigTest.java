package com.bees360.config;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectCreation;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.mapper.ProjectCreationMapper;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

class BeanConfigTest {

    private ObjectMapper objectMapper = newObjectMapper();

    private ObjectMapper newObjectMapper() {
        var mapper = new ObjectMapper(new YAMLFactory());
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.KEBAB_CASE);
        return mapper;
    }

    @SneakyThrows
    @Test
    void testProjectAutoClientReceivedPredicate() {
        ProjectCreationMapper mapper = Mockito.mock(ProjectCreationMapper.class);
        when(mapper.getByProjectId(anyLong())).thenReturn(new ProjectCreation().setCreationChannel(CreationChannelType.WEB.name()));
        Bees360CompanyConfig companyConfig = objectMapper.readValue(loadResource("company-config.yaml"), Bees360CompanyConfig.class);

        var predicate = new BeanConfig().projectAutoClientReceivedPredicate(mapper, companyConfig, new Bees360FeatureSwitch(), (id) -> true);
        assertTrue(predicate.test(newProject(1L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove is allowed when applying global config.");
        assertFalse(predicate.test(newProject(2L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove isn't allowed when auto-client-received is false.");
        assertTrue(predicate.test(newProject(3L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove is allowed when auto-client-received is true.");
        assertFalse(predicate.test(newProject(3L, ProjectServiceTypeEnum.SCHEDULING_ONLY)), "Scheduling only isn't allowed when auto-client-received is true and exclude Scheduling One.");

        when(mapper.getByProjectId(eq(1L))).thenReturn(new ProjectCreation().setCreationChannel(CreationChannelType.OPENAPI.name()));
        assertFalse(predicate.test(newProject(1L, ProjectServiceTypeEnum.EXPRESS_INSPECTION)), "Project from OPENAPI is not allowed.");

        assertTrue(predicate.test(newProject(5L, ProjectServiceTypeEnum.WHITE_GLOVE)));
        assertFalse(predicate.test(newProject(5L, ProjectServiceTypeEnum.SCHEDULING_ONLY)));
    }

    private Project newProject(Long insuredBy, ProjectServiceTypeEnum serviceType) {
        var project = new Project();
        project.setProjectId(100);
        project.setInsuranceCompany(insuredBy);
        project.setServiceType(serviceType.getCode());
        return project;
    }

    @SneakyThrows
    private static String loadResource(String name) {
        return IOUtils.resourceToString(name, StandardCharsets.UTF_8, BeanConfigTest.class.getClassLoader());
    }
}
