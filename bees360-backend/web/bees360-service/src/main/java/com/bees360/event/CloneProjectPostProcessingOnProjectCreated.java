package com.bees360.event;

import com.bees360.entity.ProjectImage;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.ImageCloneJob;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.util.SecureTokens;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import jakarta.annotation.Nullable;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

import static com.bees360.service.impl.ProjectImageServiceImpl.GROUP_TYPE_PROJECT;


@Log4j2
public class CloneProjectPostProcessingOnProjectCreated extends AbstractNamedEventListener<ProjectCreatedEvent> {

    private final ProjectIIManager projectIIManager;

    private final ProjectImageMapper projectImageMapper;

    private final JobScheduler jobScheduler;

    private final ProjectStatusManager projectStatusManager;

    public CloneProjectPostProcessingOnProjectCreated(
            ProjectIIManager projectIIManager,
            ProjectImageMapper projectImageMapper,
            JobScheduler jobScheduler,
            ProjectStatusManager projectStatusManager
    ) {
        this.projectIIManager = projectIIManager;
        this.projectImageMapper = projectImageMapper;
        this.jobScheduler = jobScheduler;
        this.projectStatusManager = projectStatusManager;
        log.info("Created {}(projectIIManager={}, projectImageMapper={}, jobScheduler={}, " +
                        "projectStatusManager={}).",
                this,
                this.projectIIManager,
                this.projectImageMapper,
                this.jobScheduler,
                this.projectStatusManager);
    }

    @Override
    public void handle(ProjectCreatedEvent event) throws IOException {
        var projectId = event.getProjectId();
        var project = projectIIManager.findById(projectId);
        var creator = project.getCreateBy().getId();
        var metadata = project.getMetadata();
        if (metadata.getCloneType() != Message.CloneType.PROCESSOR_TRAINING) {
            return;
        }

        var cloneFrom = metadata.getCloneFrom().getValue();
        Preconditions.checkArgument(
            StringUtils.isNoneBlank(cloneFrom),
            "Clone from project id is blank. project id: {}", projectId);

        log.info("Start clone project post processing. cloneFrom:{} projectId:{}", cloneFrom, projectId);

        var oldProjectImages = projectImageMapper.listAll(Long.parseLong(cloneFrom));
        if (CollectionUtils.isEmpty(oldProjectImages)) {
            log.info("Project({}) has no images", cloneFrom);
            return;
        }

        // 处理 project image 数据
        var newProjectImages = new ArrayList<ProjectImage>();
        Map<String, Set<String>> imageGroupMap = Map.of(
            GROUP_TYPE_PROJECT,
            Set.of(projectId)
        );
        var cloneImages = new ArrayList<ImageCloneJob.CloneImage>();
        for (ProjectImage oldImage : oldProjectImages) {
            var oldImageId = oldImage.getImageId();
            var newImageId = SecureTokens.generateRandomBase64Token();

            var newImage = (ProjectImage) oldImage.clone();
            newImage.setProjectId(Long.parseLong(projectId));
            newImage.setImageId(newImageId);

            cloneImages.add(new ImageCloneJob.CloneImage(oldImageId, newImageId, imageGroupMap));
            newProjectImages.add(newImage);
        }
        // save solid project image
        var job = new ImageCloneJob(cloneImages, String.valueOf(creator));
        var future = jobScheduler.schedule(Job.ofPayload(job));

        Futures.addCallback(
            future,
            new FutureCallback<>() {
                @Override
                public void onSuccess(@Nullable Void result) {
                    log.info("Success to clone project {} image with job {}.", projectId, job);
                    // save web project image
                    projectImageMapper.insertBaseInfoList(newProjectImages);
                    // 触发 project status - Image uploaded
                    projectStatusManager.updateStatus(projectId, com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED, String.valueOf(creator));
                    log.info("Successfully clone project images. cloneFrom:{} projectId:{}", cloneFrom, projectId);
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Failed to clone project %s image with job %s.".formatted(projectId, job), t);
                }
            },
            MoreExecutors.directExecutor());
    }
}
