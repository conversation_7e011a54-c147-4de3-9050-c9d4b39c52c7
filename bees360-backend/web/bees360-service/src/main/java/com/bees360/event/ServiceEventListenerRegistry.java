package com.bees360.event;

import com.bees360.activity.ActivityManager;
import com.bees360.address.AddressManager;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.job.JobScheduler;
import com.bees360.mapper.AddressAirspaceMapper;
import com.bees360.mapper.ProjectAirspaceMapper;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectImageTagMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectStateMapper;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ContactManager;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.firebase.FirebaseProjectService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

import static com.bees360.entity.enums.PipelineTaskEnum.CHECK_PILOT_AVAILABILITY;
import static com.bees360.entity.enums.PipelineTaskEnum.KEEP_PROJECT_ON_TRACK;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_DRONE_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_EXTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_HOVER_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_INTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_MAGICPLAN_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_PLNAR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_DRONE_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_EXTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_INTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.VERIFY_ADDRESS;

@Configuration
@Import({
    CompleteInvoiceTaskOnProjectInvoiceUpdated.class,
    SetStageOwnerOnProjectOMChanged.class,
    CustomerContactedEventListener.class,
    ChangePipelineOnInteriorDamage.class,
    ChangeServiceTypeOnProjectCreated.class,
    SetProjectPaidOnProjectPaidEvent.class,
    BatchRemovePilotEvent2Job.class,
    BatchAssignPilotEvent2Job.class,
    PilotMissionRework2Job.class,
    MissionCompleted2Job.class,
    DoUpdateOnInspectionDueDateChanged.class,
    DoUpdateOnInspectionScheduledTimeChanged.class,
    UpdateOperationTagOnIBeesNotComplete.class,
    SetProjectPayStatusOnProjectPaymentChangedEvent.class,
    UpdateMySqlOnOperatingCompanyChanged.class,
    AddActivityOnProjectAddressChangedEvent.class,
})
public class ServiceEventListenerRegistry {

    @Bean
    @ConditionalOnProperty(prefix = "bees360.feature-switch", name = "enableSyncFirebaseContact", havingValue = "true", matchIfMissing = true)
    UpdateFirebaseOnContactChangedEvent updateFirebaseOnContactChangedEvent(
            FirebaseProjectService firebaseProjectService,
            ContactManager contactManager) {
        return new UpdateFirebaseOnContactChangedEvent(firebaseProjectService, contactManager);
    }

    @Bean
    public SetTaskDoneOnTaskReady setTaskDoneOnTaskReady(PipelineService pipelineService) {
        return new SetTaskDoneOnTaskReady(
                Set.of(PipelineTaskEnum.PROJECT_CREATE.getKey()), pipelineService);
    }

    @Bean
    public SetTaskOngoingOnTaskReady setTaskOngoingOnTaskReady(PipelineService pipelineService) {
        return new SetTaskOngoingOnTaskReady(
                Set.of(
                        UPLOAD_INTERIOR_IMAGES.getKey(),
                        UPLOAD_EXTERIOR_IMAGES.getKey(),
                        UPLOAD_DRONE_IMAGES.getKey(),
                        TAKE_DRONE_IMAGES.getKey(),
                        TAKE_EXTERIOR_IMAGES.getKey(),
                        TAKE_INTERIOR_IMAGES.getKey(),
                        TAKE_HOVER_IMAGES.getKey(),
                        TAKE_MAGICPLAN_IMAGES.getKey(),
                        TAKE_PLNAR_IMAGES.getKey(),
                        VERIFY_ADDRESS.getKey(),
                        KEEP_PROJECT_ON_TRACK.getKey(),
                        CHECK_PILOT_AVAILABILITY.getKey()),
                pipelineService);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "bees360.feature-switch",
            name = "enableFirebaseOpenClose",
            havingValue = "true")
    public ChangeFirebaseProjectStateOnStateChanged changeFirebaseProjectStateOnStateChanged(
            FirebaseProjectService firebaseProjectService) {
        return new ChangeFirebaseProjectStateOnStateChanged(firebaseProjectService);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "bees360.feature-switch",
            name = "enableOpenCloseWrite",
            havingValue = "true")
    public UpdateMysqlStateOnProjectStateChanged updateMysqlStateOnProjectStateChanged(
            ProjectStateMapper projectStateMapper) {
        return new UpdateMysqlStateOnProjectStateChanged(projectStateMapper);
    }

    @Configuration
    @Import({
        UpdateProjectOnProjectStatusChanged.class,
        InsertProjectStatusOnProjectStatusEvent.class,
        ClearProjectTagsOnProjectStatusChanged.class,
        CompletePilotOnProjectReturnedToClient.class,
        RemoveTagOnProjectIBeesUploaded.class,
    })
    static class StatusListenerConfig {}

    @Configuration
    @ConditionalOnProperty(
            prefix = "bees360.feature-switch",
            name = "enable-set-inspection-stage-owner",
            havingValue = "true",
            matchIfMissing = true)
    @Import(SetStageOwnerOnProjectOMChanged.class)
    static class SetStageOwnerOnProjectOMChangedConfig {}

    @Configuration
    @ConditionalOnProperty(
            prefix = "bees360.feature-switch",
            name = "enable-status-qc",
            havingValue = "true",
            matchIfMissing = true)
    @Import({
        AddSiteInspectedOnProjectStatusChanged.class,
    })
    static class SetStatusQCOnProjectStatusChangedConfig {

        @Bean
        AddCustomerContactedOnProjectStatusAssignedToPilot addCustomerContactedOnProjectStatusAssignedToPilot(
            EventPublisher eventPublisher,
            ProjectService projectService,
            ProjectStatusService projectStatusService
        ) {
            return new AddCustomerContactedOnProjectStatusAssignedToPilot(
                eventPublisher,
                projectService,
                projectStatusService,
                Set.of(ProjectServiceTypeEnum.EXPRESS_INSPECTION));
        }
    }

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-remove-tag-on-project-interior-image-uploaded",
        havingValue = "true")
    @Import(RemoveTagOnProjectInteriorImageUploaded.class)
    static class RemoveTagOnProjectInteriorImageUploadedConfig {}

    @Bean
    @ConditionalOnProperty(
            prefix = "bees360.feature-switch",
            name = "enable-project-address-formatted-activity",
            havingValue = "true",
            matchIfMissing = true)
    public ProjectAddressFormattedEventListener projectAddressFormattedEventListener(
            ActivityManager activityManager, AddressManager addressManager) {
        return new ProjectAddressFormattedEventListener(activityManager, addressManager);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "bees360.feature-switch",
            name = "enable-client-received-on-towerhill-resource-deleted",
            havingValue = "true")
    public ClientReceivedOnCustomerResourceDeleted clientReceivedOnTowerHillResourceDeleted(
            ProjectIIRepository projectIIRepository, ProjectStatusService projectStatusService) {
        // 注意: 此处要与 towerHillDeliverableResourceKeyDecoder 保持同一命名规则
        Function<String, String> towerHillDeliverableResourceKeyDecoder =
                deliverableResourceKey -> StringUtils.split(deliverableResourceKey, "_")[1];
        Predicate<String> isProjectNeedToBeReceived =
                projectId -> {
                    var project = projectIIRepository.get(projectId);
                    var state = project.getCurrentState().getState();
                    var status = project.getLatestStatus();
                    // Project未关闭且不是CLIENT_RECEIVED才更新状态
                    return !Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE
                            .equals(state)
                            && !Message.ProjectStatus.CLIENT_RECEIVED.equals(status);
                };
        String towerHillNameSpace = "tower_hill";

        return new ClientReceivedOnCustomerResourceDeleted(
                towerHillNameSpace,
                projectStatusService,
                towerHillDeliverableResourceKeyDecoder,
                isProjectNeedToBeReceived);
    }

    @Bean
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-update-address-airspace",
        havingValue = "true",
        matchIfMissing = true)
    public UpdateMysqlAddressAirspaceOnProjectAirspaceUpdated updateAddressAirspaceOnProjectAirspaceUpdated(
        AddressAirspaceMapper addressAirspaceMapper, ProjectService projectService) {
        return new UpdateMysqlAddressAirspaceOnProjectAirspaceUpdated(addressAirspaceMapper, projectService);
    }

    @Bean
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-update-project-airspace",
        havingValue = "true")
    public UpdateMysqlProjectAirspaceOnProjectAirspaceUpdated updateMysqlProjectAirspaceOnProjectAirspaceUpdated(
            ProjectAirspaceMapper projectAirspaceMapper) {
        return new UpdateMysqlProjectAirspaceOnProjectAirspaceUpdated(projectAirspaceMapper);
    }

    @Bean
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-update-project-on-contact-changed",
        havingValue = "true",
        matchIfMissing = true)
    public UpdateProjectOnContactChangedEvent updateProjectOnContactChangedEvent(
        ProjectService projectService, ProjectMapper projectMapper) {
        return new UpdateProjectOnContactChangedEvent(projectService, projectMapper);
    }

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-created-event",
        havingValue = "false")
    @Import({
        AddPolicyCoverageActivityOnProjectCreated.class,
        AutoHailTagOnProjectCreated.class,
    })
    static class ProjectCreatedListenerCloseConfig {
        @Bean
        AddActivityOnProjectCreated addActivityOnProjectCreated(
            ActivityManager activityManager,
            ProjectService projectService,
            ProjectMessageService projectMessageService) {
            return new AddActivityOnProjectCreated(
                activityManager,
                projectService,
                projectMessageService);
        }
    }

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-created-event",
        havingValue = "true")
    @Import({
        PublishSpringCreatedEventOnProjectCreated.class,
    })
    static class ProjectCreatedListenerOpenConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-publish-project-comment-add",
        havingValue = "true",
        matchIfMissing = true
    )
    @Import({
        PublishProjectCommentAddedOnCommentAdded.class,
        PublishProjectCommentAddedOnActivityAdded.class,
    })
    static class publishProjectCommentAddedConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-update-project-note-new",
        havingValue = "true"
    )
    @Import({
        UpdateProjectNoteOnActivityChanged.class,
        UpdateProjectNoteOnCommentChanged.class,
    })
    static class ProjectNoteUpdateConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-init-project-loss-description-new",
        havingValue = "true"
    )
    @Import({
        InitLossDescriptionOnActivityChanged.class,
        InitLossDescriptionOnCommentChanged.class,
    })
    static class InitLossDescriptionConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-clone-project-post-processing-on-created-event",
        havingValue = "true",
        matchIfMissing = true)
    static class CloneProjectPostProcessingOnProjectCreatedConfig {

        @Bean
        CloneProjectPostProcessingOnProjectCreated cloneProjectPostProcessingOnProjectCreated(
            ProjectIIManager projectIIManager,
            ProjectImageMapper projectImageMapper,
            JobScheduler jobScheduler,
            ProjectStatusManager projectStatusManager,
            ProjectImageTagMapper projectImageTagMapper
        ) {
            return new CloneProjectPostProcessingOnProjectCreated(
                projectIIManager,
                projectImageMapper,
                jobScheduler,
                projectStatusManager,
                projectImageTagMapper
            );
        }
    }

}
