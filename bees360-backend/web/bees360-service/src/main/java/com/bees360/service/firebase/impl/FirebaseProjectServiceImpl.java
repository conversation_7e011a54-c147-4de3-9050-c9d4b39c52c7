package com.bees360.service.firebase.impl;

import static com.bees360.activity.Message.ActivityMessage.EntityType.PROJECT;
import static com.bees360.activity.Message.ActivityMessage.FieldName.STATUS;
import static com.bees360.entity.enums.NewProjectStatusEnum.ASSIGNED_TO_PILOT;
import static com.bees360.entity.enums.NewProjectStatusEnum.CLIENT_RECEIVED;
import static com.bees360.entity.enums.NewProjectStatusEnum.IMAGE_UPLOADED;
import static com.bees360.entity.enums.NewProjectStatusEnum.RETURNED_TO_CLIENT;
import static com.bees360.entity.enums.NewProjectStatusEnum.SITE_INSPECTED;
import static com.bees360.service.firebase.FirebaseService.PILOT_COLLECTION;
import static com.bees360.service.firebase.FirebaseService.PROJECT_COLLECTION;

import co.realms9.bifrost.UserManager;
import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Comment;
import com.bees360.address.AddressProvider;
import com.bees360.atomic.LockProvider;
import com.bees360.atomic.RedisLockProvider;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.function.DoubleParameterConsumer;
import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectInspection;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.User;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectMessageTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.firebase.FirebaseLeaveMessage;
import com.bees360.entity.firebase.FirebaseProjectStatusEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.query.ProjectFilterQuery;
import com.bees360.entity.query.ProjectMessageQuery;
import com.bees360.entity.query.ProjectStatusQuery;
import com.bees360.entity.vo.request.ProjectServiceTypeParam;
import com.bees360.firebase.domain.InsuredSurvey;
import com.bees360.firebase.domain.InsuredSurvey.FirebaseInsuredSurveyStatus;
import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectII;
import com.bees360.project.state.ProjectState;
import com.bees360.repository.Provider;
import com.bees360.service.ActivityService;
import com.bees360.service.CompanyService;
import com.bees360.service.MemberService;
import com.bees360.service.ProjectInspectionService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import com.bees360.util.JsonUtils;
import com.bees360.web.event.project.claim.ProjectClaimLeaveMessageEvent;
import com.bees360.web.project.util.ProjectConstants;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.FieldValue;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.GeoPoint;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.common.base.Preconditions;
import jakarta.annotation.Nullable;
import java.time.Duration;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Log4j2
@Service
public class FirebaseProjectServiceImpl implements FirebaseProjectService {
    @Autowired private ProjectMapper projectMapper;
    @Autowired private ProjectService projectService;
    @Autowired private ProjectLabelService projectLabelService;
    @Autowired private Firestore firestore;
    @Autowired private ProjectMessageService projectMessageService;
    @Autowired private ActivityService activityService;
    @Autowired private CompanyService companyService;
    @Autowired private ApplicationEventPublisher publisher;
    @Autowired private ProjectInspectionService projectInspectionService;
    @Autowired private ProjectStatusService projectStatusService;
    @Autowired private ProjectStatusMapper projectStatusMapper;
    @Autowired private FirebaseService firebaseService;
    @Autowired private Bees360CompanyConfig bees360CompanyConfig;
    @Autowired private ActivityManager activityManager;
    @Autowired private MemberService memberService;
    @Autowired private Provider<ProjectII> projectIIProvider;
    @Autowired private ContactManager contactManager;
    @Autowired private AddressProvider addressProvider;
    @Autowired private UserManager bifrostUserManager;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    private final LockProvider lockProvider;
    private final LockProvider firebaseProjectStatusLockProvider;

    public FirebaseProjectServiceImpl(RedissonClient redissonClient) {
        lockProvider = new RedisLockProvider(redissonClient, getClass().getName(), Duration.ofMinutes(1));
        firebaseProjectStatusLockProvider = new RedisLockProvider(redissonClient, "FIREBASE_PROJECT_STATUS_LOCK", Duration.ofMinutes(1));
    }

    @Override
    public void handleFirebaseProject(
            SerializableFirebaseProject firebaseProject, String projectIdStr) {

        // 同步基本信息到web
        long projectId = Long.parseLong(projectIdStr);
        try (var ignored = lockProvider.lock(projectIdStr)) {
            updateProjectInfoFromFirebase2Web(firebaseProject);

            // 如果project status状态是bees360触发的不处理
            if (SystemTypeEnum.BEES360
                    .getType()
                    .equals(firebaseProject.getProject_status_update_by())) {
                return;
            }
            // Firebase Project 状态更改并同步到bees360 web
            // 注意contact customer不是在这里触发的
            FirebaseProjectStatusEnum projectStatusEnum =
                    FirebaseProjectStatusEnum.getEnum(firebaseProject.getProject_status());
            if (projectStatusEnum != null) {
                updateProjectStatusFromFirebase2Web(
                    projectId,
                    projectStatusEnum,
                    firebaseProject.getStatus_update_time());
            }
        }
    }

    @Override
    public void updateFirebaseProjectBaseInfo(long projectId) throws ServiceException {
        updateFirebaseProjectField(projectService.getById(projectId), this::setProjectBaseInfo);
    }

    @Override
    public void updateFirebaseProjectStatus(
        Project project, FirebaseProjectStatusEnum statusEnum) {
        long projectId = project.getProjectId();
        NewProjectStatusEnum realProjectStatusEnum =
                NewProjectStatusEnum.getEnum(project.getProjectStatus());
        log.info(
                "Start to change firebase project '{}' status to '{}' because '{}' is happened.",
                projectId,
                realProjectStatusEnum,
                statusEnum);

        updateFirebaseProjectStatus(project.getProjectId());
        log.info(
                "Success change firebase project '{}' status to '{}' because '{}' is happened.",
                projectId,
                realProjectStatusEnum,
                statusEnum);
    }

    private void updateFirebaseProjectStatus(long projectId) {
        try (var ignored = firebaseProjectStatusLockProvider.lock(String.valueOf(projectId))) {
            var project = projectService.getById(projectId);
            updateFirebaseProjectField(project, this::setProjectStatus);
        }
    }

    @Override
    public void updateFirebaseProjectLatestStatus(Project project) {
        updateFirebaseProjectField(project, this::setProjectLatestStatus);
    }

    @Override
    public void syncSurveyCompletedToFirebase(long projectId) {
        try {
            List<QueryDocumentSnapshot> documents =
                    firestore
                            .collection(InsuredSurvey.COLLECTION_NAME)
                            .whereEqualTo("projectId", String.valueOf(projectId))
                            .get()
                            .get()
                            .getDocuments();
            if (CollectionAssistant.isEmpty(documents)) {
                return;
            }
            for (QueryDocumentSnapshot document : documents) {
                DocumentReference reference = document.getReference();
                reference.update("hasComplete", true);
                reference.update("status", FirebaseInsuredSurveyStatus.COMPLETED.getCode());
            }
        } catch (ExecutionException | InterruptedException e) {
            String message = "Failed to complete firebase insured_survey. %d";
            message = message.formatted(projectId);
            throw new IllegalStateException(message, e);
        }
    }

    @Override
    public void syncServiceCompletedToFirebase(long projectId) {
        try {
            List<QueryDocumentSnapshot> documents =
                    firestore
                            .collection(InsuredSurvey.COLLECTION_NAME)
                            .whereEqualTo("projectId", String.valueOf(projectId))
                            .get()
                            .get()
                            .getDocuments();
            if (CollectionAssistant.isEmpty(documents)) {
                return;
            }
            for (QueryDocumentSnapshot document : documents) {
                DocumentReference reference = document.getReference();
                reference.update("serviceCompleted", true);
            }
        } catch (ExecutionException | InterruptedException e) {
            String message = "Failed to syncServiceCompleted firebase insured_survey. %d";
            message = message.formatted(projectId);
            throw new IllegalStateException(message, e);
        }
    }

    @Override
    public void initFirebaseProject(Project project) {
        if (bees360FeatureSwitch.isEnableRabbitJobInitFirebaseProject()) {
            firebaseService.syncProjectToFirebase(project.getProjectId());
            return;
        }

        long projectId = project.getProjectId();
        String documentPath = firebaseService.getProjectDocumentPath(projectId);
        var map = getAllFieldsInProject(project);
        firebaseService.syncProjectToFirebaseWithFailedRetry(projectId, documentPath, map, false);
    }

    private void setProjectState(Map<String, Object> map, ProjectState state) {
        var stateName = state.getState().name().toLowerCase();
        var stateChangeReasonId = state.getStateChangeReason().getId();
        // stop sync display_text of project_state_change_reason to firebase
        map.put("project_state", stateName);
        map.put("state_change_reason_id", stateChangeReasonId);
    }

    @Override
    public void syncProjectStateToFirebase(long projectId, ProjectState state) {
        Map<String, Object> projectFieldsMap = new HashMap<>();
        setProjectState(projectFieldsMap, state);
        String documentPath = firebaseService.getProjectDocumentPath(projectId);
        firebaseService.syncProjectToFirebaseWithFailedRetry(projectId, documentPath, projectFieldsMap, true);
    }

    @Override
    public Map<String, Object> getAllFieldsInProject(Project project) {
        Map<String, Object> projectFieldsMap = new HashMap<>();
        setProjectBaseInfo(projectFieldsMap, project);
        setProjectSolidInfo(projectFieldsMap, project);
        setInitProjectStatus(projectFieldsMap, project);
        setProjectRelatedData(projectFieldsMap, project);
        setProjectLatestStatus(projectFieldsMap, project);
        setProjectIIInfo(projectFieldsMap, project.getProjectId());
        setContactFields(projectFieldsMap, project.getProjectId());
        return projectFieldsMap;
    }

    private void updateFirebaseProjectField(
            Project project, DoubleParameterConsumer<Map<String, Object>, Project> setField) {
        Map<String, Object> projectFieldsMap = new HashMap<>();
        long projectId = project.getProjectId();
        setField.accept(projectFieldsMap, project);
        projectFieldsMap.put("update_time", System.currentTimeMillis());
        firebaseService.syncProjectToFirebaseWithFailedRetry(
                projectId,
                firebaseService.getProjectDocumentPath(projectId),
                projectFieldsMap,
                true);
    }

    public void recoverProjectLatestStatus() {
        for (DocumentReference documentReference :
                firestore.collection(PROJECT_COLLECTION).listDocuments()) {
            try {
                doRecoverProjectLatestStatus(documentReference.getId());
            } catch (RuntimeException  e) {
                log.warn("Failed to update project '{}' latest status", documentReference.getId(), e);
            }
        }
    }

    @Override
    public void initLossDescription(Project project, Comment comment) {
        if (project == null || comment == null) {
            return;
        }
        var projectId = project.getProjectId();
        try {
            DocumentReference reference =
                    firestore.document(firebaseService.getProjectDocumentPath(projectId));
            var lossDescription = (String) reference.get().get().get("loss_description");
            var insuranceCompany = project.getInsuranceCompany();
            if (Strings.isNotBlank(lossDescription)) {
                log.info(
                        "Skip to initial project '{}' loss description({}) because it is existed.",
                        projectId,
                        lossDescription);
                return;
            }
            var newLossDesc = getLossDescription(projectId, comment.getContent(), insuranceCompany);
            if (StringUtils.isNotEmpty(newLossDesc)) {
                updateFirebaseProjectField(
                        project, (map, unused) -> map.put("loss_description", newLossDesc));
                log.info(
                        "Successfully set loss description '{}' to project '{}'.",
                        lossDescription,
                        projectId);
            }
        } catch (InterruptedException | ExecutionException e) {
            // 如果项目在firebase中不存在loss_description，则不需要更新.
            log.error("Failed to set loss description for Project '{}' in firebase.", projectId, e);
        }
    }

    private void doRecoverProjectLatestStatus(String id) {
        Long projectId = Long.valueOf(id);
        Project project = projectService.getById(projectId);
        updateFirebaseProjectLatestStatus(project);
        log.info("Successfully update project '{}' latest status '{}'", id, project.getLatestStatus());
    }
    @Override
    public void updateFirebaseProjectRelateInfo(long projectId) throws ServiceException {
        Project project = projectService.getById(projectId);
        DocumentReference reference =
                firestore.document(firebaseService.getProjectDocumentPath(projectId));
        if (project != null) {
            Long tagId = null;
            try {
                tagId = (Long) reference.get().get().get("tag_id");
            } catch (InterruptedException | ExecutionException e) {
                // 如果项目在firebase中不存在，下面的逻辑会将项目的全部字段全部到firebase.
                log.warn("Project '{}' is not existed in firebase.", projectId, e);
            }
            List<Long> newTag =
                    Optional.ofNullable(tagId)
                            .map(Collections::singletonList)
                            .orElse(Collections.emptyList());
            List<Long> projectLabels =
                    projectLabelService
                            .projectLabel(projectId)
                            .map(BoundProjectLabel::getProjectLabels)
                            .map(
                                    a ->
                                            a.stream()
                                                    .map(ProjectLabel::getLabelId)
                                                    .collect(Collectors.toList()))
                            .orElseGet(Collections::emptyList);
            // 在更新之前确保两端数据不一样, 否则会增多写操作，可能会造成循环更新.
            if (!Objects.equals(newTag, projectLabels)) {
                updateFirebaseProjectField(project, this::setProjectRelatedData);
            }
        }
    }

    @Override
    public void syncCatNumberToFirebase(List<Project> projects) {
        for (Project project : projects) {
            Preconditions.checkArgument(project.getProjectId() != 0);
            updateFirebaseProjectField(project, this::setCatNumber);
        }
    }

    @Override
    public void recoverTimeline(Long projectIdStart, Long projectIdEnd) {
        var query =
            ProjectFilterQuery.builder()
                .projectIdStart(projectIdStart)
                .projectIdEnd(projectIdEnd)
                .build();
        var projectIds = projectMapper.listProjectId(query);
        projectIds.forEach(this::doRecoverTimeline);
    }

    private void doRecoverTimeline(Long projectId) {
        var query = ProjectStatusQuery.builder().projectId(projectId).build();
        var statuses = projectStatusMapper.listWithQuery(query);
        if (CollectionUtils.isEmpty(statuses)) {
            log.info("Skipped to recover project '{}' time maybe it is deleted.",projectId);
            return;
        }
        var activities = getActivity(projectId);
        recoverStatusHistory(projectId, statuses, activities, ASSIGNED_TO_PILOT);
        recoverStatusHistory(projectId, statuses, activities, SITE_INSPECTED);
        recoverStatusHistory(projectId, statuses, activities, IMAGE_UPLOADED);
        recoverStatusHistory(projectId, statuses, activities, RETURNED_TO_CLIENT);
        recoverStatusHistory(projectId, statuses, activities, CLIENT_RECEIVED);
    }

    private void recoverStatusHistory(
        long projectId,
        List<ProjectStatus> allStatus,
        List<? extends Activity> source,
        NewProjectStatusEnum statusEnum) {
        try {
            log.info("Start to recover '{}' '{}' time'.", projectId,statusEnum.getDisplay());
            doRecoverStatusHistory(projectId, allStatus, source, statusEnum);
        } catch (RuntimeException e) {
            log.warn("Failed to recover '{}' '{}' time.", projectId, statusEnum.getDisplay(), e);
        }

    }
    private void doRecoverStatusHistory(
        long projectId,
        List<ProjectStatus> allStatus,
        List<? extends Activity> source,
        NewProjectStatusEnum statusEnum) {
        var statusName = statusEnum.getDisplay();
        var trueSiteInspected = getStatusCreatedTimeInMilli(source.stream(), projectId, statusName);
        if (trueSiteInspected == null) {
            log.info(
                "Skipped to recover '{}' '{}' time because activity is null.",
                projectId,statusName);
            return;
        }

        boolean inserted = false;
        var statuses = allStatus.stream()
            .filter(status -> SITE_INSPECTED.getCode() == status.getStatus())
            .sorted(Comparator.comparing(ProjectStatus::getCreatedTime))
            .collect(Collectors.toList());
        var userId = statuses.stream().findFirst().map(ProjectStatus::getUserId).orElse(User.AI_ID);
        var existed =
            statuses.stream()
                .map(ProjectStatus::getCreatedTime)
                .anyMatch(
                    time ->
                        Duration.between(
                                trueSiteInspected,
                                Instant.ofEpochMilli(time))
                            .abs()
                            .toSeconds()
                            <= 5);
        for (ProjectStatus status : statuses) {
            var temp = Instant.ofEpochMilli(status.getCreatedTime());
            var gap = Duration.between(trueSiteInspected, temp);
            if (gap.abs().toSeconds() <= 5) {
                continue;
            }

            if (gap.isNegative()) {
                // remove the status history
                projectStatusMapper.delete(status);
            }
        }
        if (existed) {
            log.info("Skipped to recover project '{}' '{}' time because it is already existed.",projectId,statusName);
            return;
        }

        // add a new status history
        var status =
            ProjectStatus.builder()
                .status(statusEnum.getCode())
                .projectId(projectId)
                .createdTime(trueSiteInspected.toEpochMilli())
                .userId(userId)
                .build();
        projectStatusMapper.insert(status);
        log.info("Successfully set project '{}' '{}' time to {}.", projectId, statusName, trueSiteInspected);
    }

    private List<? extends Activity> getActivity(long projectId) {
        return activityManager.getActivities(
            ActivityQuery.builder()
                .projectId(projectId)
                .entityType(PROJECT.name())
                .fieldName(STATUS.name())
                .sortBy(Collections.singletonList("createdAt"))
                .sortDirection("asc")
                .build());
    }

    @Nullable
    private Instant getStatusCreatedTimeInMilli(
        Stream<? extends Activity> source, long projectId, String status) {
        return source.filter(activity -> activity.getProjectId() == projectId)
            .filter(activity ->status.equals(activity.getValue()))
            .findFirst()
            .map(Activity::getCreatedAt)
            .orElse(null);
    }

    private void setCatNumber(Map<String, Object> fields, Project project) {
        fields.put("cat_number", project.getCatNumber());
    }

    /** 设置项目不可变字段 */
    private void setProjectSolidInfo(Map<String, Object> fields, Project project) {
        long projectId = project.getProjectId();
        long creatorId = project.getCreatedBy();
        var insuranceCompany = project.getInsuranceCompany();
        var claimNote = project.getClaimNote();
        String creator = Optional.ofNullable(bifrostUserManager.getUserById(creatorId + ""))
            .map(co.realms9.bifrost.User::getName)
            .orElse("");
        var lossDescription = getLossDescription(projectId, claimNote, insuranceCompany);
        fields.put("project_id", projectId + "");
        fields.put(
                "cat_number",
                Strings.isBlank(project.getCatNumber()) ? null : project.getCatNumber());
        fields.put("creator_id", creatorId);
        fields.put("creator_name", creator);
        fields.put("created_time", project.getCreatedTime());
        fields.put("inspection_code", project.getInspectionCode());
        fields.put("expiration_time", project.getExpirationTime());
        fields.put("loss_description", lossDescription);
        fields.put("tag", null);
    }

    private void setProjectIIInfo(Map<String, Object> fieldsMap, Long projectId) {
        var projectII = projectIIProvider.get(String.valueOf(projectId));
        var policy = projectII.getPolicy();
        var state = projectII.getCurrentState();

        fieldsMap.put("supplemental_service", projectII.getSupplementalService());
        fieldsMap.put("is_renewal", policy.isRenewal());
        // policy
        fieldsMap.put("policy_type", policy.getType());
        setProjectState(fieldsMap, state);
    }

    /**
     * 设置项目状态相关的数据 如果有字段更改会影响项目状态的更改也需要在这里设置
     *
     * @param fields FirebaseProject字段
     * @param project 项目状态
     */
    private void setProjectStatus(Map<String, Object> fields, Project project) {
        long projectId = project.getProjectId();
        fields.put("project_status", project.getProjectStatus());
        fields.put("status_update_time", project.getStatusUpdateTime());
        fields.put("project_status_update_by", SystemTypeEnum.BEES360.getType());
        Optional.ofNullable(latestContactTime(projectId)).ifPresent(time -> fields.put("contact_time", time));
        fields.put("tag_id", getLabelId(projectId));
        fields.put("note", activityService.getNote(project.getProjectId()));
        fields.put("service_type", Optional.ofNullable(project.getServiceType()).orElse(0));
    }

    private void setProjectLatestStatus(Map<String, Object> fields, Project project) {
        fields.put("latest_status", project.getLatestStatus());
    }


    /**
     * 设置初始化项目状态相关的数据
     *
     * @param fields FirebaseProject字段
     * @param project 项目状态
     */
    private void setInitProjectStatus(Map<String, Object> fields, Project project) {
        long projectId = project.getProjectId();
        setProjectStatus(fields, project);
        fields.put("contact_time", latestContactTime(projectId));
    }

    /**
     * 设置项目相关字段，这些字段没有直接存在Bees360的project表里
     *
     * @param fields FirebaseProject字段
     * @param project 项目
     */
    private void setProjectRelatedData(Map<String, Object> fields, Project project) {
        long projectId = project.getProjectId();
        fields.put("tag_id", getLabelId(projectId));
        fields.put("note", activityService.getNote(project.getProjectId()));
    }

    /**
     * 设置项目相关字段，这些字段没有直接存在Bees360的project表里
     *
     * @param fields FirebaseProject字段
     * @param project project
     */
    private void setProjectBaseInfo(Map<String, Object> fields, Project project) {
        Long insuranceCompanyId = project.getInsuranceCompany();
        String insuredCompanyName = null;
        String insuredCompanyLogo = null;
        if (insuranceCompanyId != null) {
            Company insuranceCompany = companyService.getById(project.getInsuranceCompany());
            insuredCompanyName =
                Optional.ofNullable(insuranceCompany)
                    .map(Company::getCompanyName)
                    .orElse(null);
            insuredCompanyLogo =
                Optional.ofNullable(insuranceCompany).map(Company::getLogo).orElse(null);
        }

        Long processCompanyId = project.getRepairCompany();
        String processCompanyName = null;
        String processCompanyLogo = null;
        if (processCompanyId != null) {
            Company processCompany = companyService.getById(processCompanyId);
            processCompanyName =
                Optional.ofNullable(processCompany)
                    .map(Company::getCompanyName)
                    .orElse(null);
            processCompanyLogo =
                Optional.ofNullable(processCompany).map(Company::getLogo).orElse(null);
        }
        fields.put("street_address", project.getAddress());
        if (bees360FeatureSwitch.isEnableFillTimeZoneWhenGetProject()) {
            var timeZone = addressProvider.findById(project.getAddressId()).getTimeZone();
            if (timeZone != null) {
                fields.put("time_zone", timeZone.getID());
            }
        }
        fields.put("city", project.getCity());
        fields.put("state", project.getState());
        fields.put("country", project.getCountry());
        fields.put("zipcode", project.getZipCode());
        fields.put(
                "gps",
                new GeoPoint(project.getGpsLocationLatitude(), project.getGpsLocationLongitude()));
        fields.put("is_gps_approximate", project.isGpsIsApproximate());
        fields.put("fly_zone_code", project.getFlyZoneType());
        fields.put("agent_name", project.getAgentContactName());
        fields.put("agent_phone", project.getAgentPhone());
        fields.put("agent_email", project.getAgentEmail());
        fields.put("claim_type", project.getClaimType());
        fields.put("service_type", Optional.ofNullable(project.getServiceType()).orElse(0));
        fields.put("insurance_company_id", insuranceCompanyId);
        fields.put("insurance_company_name", insuredCompanyName);
        fields.put("insurance_company_logo", insuredCompanyLogo);
        fields.put("insured_name", project.getAssetOwnerName());
        fields.put("insured_phone", project.getAssetOwnerPhone());
        fields.put("insured_home_phone", project.getInsuredHomePhone());
        fields.put("insured_work_phone", project.getInsuredWorkPhone());
        fields.put("insured_email", project.getAssetOwnerEmail());
        fields.put("update_time", System.currentTimeMillis());
        fields.put("policy_number", project.getPolicyNumber());
        fields.put(
                "policy_effective_date",
                Optional.ofNullable(project.getPolicyEffectiveDate())
                        .map(a -> a.format(DateTimeFormatter.BASIC_ISO_DATE))
                        .orElse(null));
        fields.put("process_company_id", processCompanyId);
        fields.put("process_company_name", processCompanyName);
        fields.put("process_company_logo", processCompanyLogo);
        fields.put("year_build", project.getYearBuilt());
        fields.put("claim_number", project.getInspectionNumber());
        fields.put("damage_event_time", project.getDamageEventTime());
        fields.put("hover_job_id", project.getHoverJobId());
        fields.put("plnar_url", project.getPlnarURL());
        fields.put("tag_id", getLabelId(project.getProjectId()));
        fields.put("test_flag", project.isTestFlag());
        fields.put("operating_company", project.getOperatingCompany());
        fields.put("property_type", project.getProjectType());

        setProjectInsuredName(fields, project);
    }

    private Long getLabelId(long projectId) {
        List<ProjectLabel> projectLabels =
                projectLabelService
                        .projectLabel(projectId)
                        .map(BoundProjectLabel::getProjectLabels)
                        .orElse(null);
        ProjectLabel label =
                Optional.ofNullable(projectLabels)
                        .flatMap(a -> a.stream().findFirst())
                        .orElse(null);
        return Optional.ofNullable(label).map(ProjectLabel::getLabelId).orElse(null);
    }

    private synchronized void updateProjectInfoFromFirebase2Web(
            SerializableFirebaseProject project) {
        // update inspectionTime
        long projectId = Long.parseLong(project.getProject_id());
        Project projectInBeesWeb = projectService.findById(projectId).orElse(null);
        if (projectInBeesWeb != null) {

            // 同步service type
            if (!Objects.equals(projectInBeesWeb.getServiceType(), project.getService_type())) {
                saveServiceType(projectId, project.getService_type());
            }

            // 同步 IBees Inspection Code and Link
            if (Strings.isBlank(projectInBeesWeb.getInspectionCode())
                    && StringUtils.isNotBlank(project.getInspection_code())
                    && Objects.nonNull(project.getExpiration_time())) {
                syncInspectionCode(project.getInspection_code(), projectId);
            }

            if (Objects.nonNull(project.getHover_job_id())
                    && !Objects.equals(
                            project.getHover_job_id(), projectInBeesWeb.getHoverJobId())) {
                updateWebProjectHoverJobId(projectId, project.getHover_job_id());
            }

            // leave vm & leave note
            saveClaimLeaveMessage(projectId, project);

            updateInitialCustomerContactTimeIfNotSet(projectInBeesWeb, project.getInitial_contact_time());
        }
    }

    private void updateInitialCustomerContactTimeIfNotSet(Project project, Long initialCustomerContactTime) {
        if (project.getInitialCustomerContactTime() == null && initialCustomerContactTime != null) {
            projectService.setInitialCustomerContactedTime(
                    project.getProjectId(), Instant.ofEpochMilli(initialCustomerContactTime));
        }
    }

    private void updateOperationsManagerInProject(Long projectId, SerializableFirebaseProject project) {
        if (StringUtils.isEmpty(project.getOperationsManagerId())) {
            return;
        }

        memberService.saveOperationsManager(
            projectId,
            project.getOperationsManagerId(),
            String.valueOf(User.BEES_PILOT_SYSTEM),
            project.getUpdate_time());
    }

    private void updateProjectStatusFromFirebase2Web(
            long projectId, FirebaseProjectStatusEnum statusEnum, long statusUpdateTime) {
        // Firebase Project 状态更改并同步到bees360 web
        Project project = projectService.getById(projectId);
        if (project == null) {
            // 项目可能已经被删除了
            log.warn("Project '%s' is not found.".formatted(projectId));
            return;
        }

        // 如果web端状态与firebase状态不一致就进行处理
        // 这里只处理assigned to pilot这一个状态
        int firebaseProjectStatus = statusEnum.getCode();
        if (project.getProjectStatus() != firebaseProjectStatus
                && statusEnum == FirebaseProjectStatusEnum.ASSIGNED_TO_PILOT) {
            log.info(
                    "Start handle status '{}' changed on firebase project '{}'.",
                    statusEnum,
                    projectId);
            projectStatusService.changeOnAssignedToPilot(
                    User.BEES_PILOT_SYSTEM, projectId, statusUpdateTime);
            log.info(
                    "Success handle status '{}' changed on firebase project '{}'.",
                    statusEnum,
                    projectId);
        }
    }

    private void syncInspectionCode(String inspectionCode, long projectId) {
        projectInspectionService.addProjectInspection(
                User.IBEES,
                ProjectInspection.builder()
                        .inspectionCode(inspectionCode)
                        .projectId(projectId)
                        .build());
    }

    private void updateWebProjectHoverJobId(Long projectId, Long hover_job_id) {
        if (Objects.isNull(hover_job_id)) {
            return;
        }
        projectMapper.updateHoverJobId(projectId, hover_job_id);
        String content = "jobId: %d for hover is created successfully".formatted(hover_job_id);
        ProjectMessage message =
                ProjectMessage.builder()
                        .createTime(System.currentTimeMillis())
                        .content(content)
                        .projectId(projectId)
                        .senderId(User.BEES_PILOT_SYSTEM)
                        .title(ProjectMessageTypeEnum.ADMIN_NOTE.getDisplay())
                        .type(ProjectMessageTypeEnum.ADMIN_NOTE.getCode())
                        .extra(String.valueOf(hover_job_id))
                        .build();
        projectMessageService.addMessageAndSyncToAiWithoutActivity(message);
        activityService.subscribeHover(
                projectId, User.BEES_PILOT_SYSTEM, String.valueOf(hover_job_id));
    }

    private void saveClaimLeaveMessage(long projectId, SerializableFirebaseProject project) {
        List<FirebaseLeaveMessage> leaveMessages = project.getClaim_leave_message();
        if (CollectionUtils.isEmpty(leaveMessages)) {
            return;
        }
        List<ProjectMessage> savedMessage =
                projectMessageService.listMessage(
                        ProjectMessageQuery.builder()
                                .projectId(projectId)
                                .senderId(User.BEES_PILOT_SYSTEM)
                                .type(ProjectMessageTypeEnum.LEAVE_NOTE.getCode())
                                .isDeleted(false)
                                .build());

        // leave message 只会追加
        FirebaseLeaveMessage needToSave = leaveMessages.get(leaveMessages.size() - 1);
        if (needToSave == null || StringUtils.isBlank(needToSave.getMessage())) {
            return;
        }
        if (!CollectionUtils.isEmpty(savedMessage)) {
            for (ProjectMessage projectMessage : savedMessage) {
                // 存在则不需要处理
                if (Objects.equals(needToSave.getMessage(), projectMessage.getContent())
                        && Objects.equals(
                                needToSave.getCreateTime(), projectMessage.getCreateTime())) {
                    return;
                }
            }
        }
        ProjectMessage message =
                ProjectMessage.builder()
                        .createTime(needToSave.getCreateTime())
                        .type(ProjectMessageTypeEnum.LEAVE_NOTE.getCode())
                        .title(ProjectMessageTypeEnum.LEAVE_NOTE.getDisplay())
                        .content(needToSave.getMessage())
                        .projectId(projectId)
                        .senderId(User.BEES_PILOT_SYSTEM)
                        .build();
        projectMessageService.addMessageAndSyncToAiWithoutActivity(message);

        publisher.publishEvent(
                new ProjectClaimLeaveMessageEvent(
                        this, projectService.getById(projectId), needToSave.getMessage()));
    }

    private String getPilotDocumentPath(String pilot) {
        return PILOT_COLLECTION + "/" + pilot;
    }

    private Long latestContactTime(long projectId) {
        List<ProjectStatus> statuses =
                projectStatusMapper.listWithQuery(
                        ProjectStatusQuery.builder()
                                .projectId(projectId)
                                .projectStatus(NewProjectStatusEnum.CUSTOMER_CONTACTED.getCode())
                                .build());
        if (!CollectionUtils.isEmpty(statuses)) {
            return statuses.stream()
                    .map(ProjectStatus::getCreatedTime)
                    .max(Comparator.naturalOrder())
                    .orElse(0L);
        }
        return null;
    }

    private void saveServiceType(long projectId, int serviceType) {
        ProjectServiceTypeParam param = new ProjectServiceTypeParam();
        param.setProjectIds(Collections.singletonList(projectId));
        param.setServiceType(serviceType);
        try {
            projectService.updateProjectServiceType(User.BEES_PILOT_SYSTEM, projectId, param);
        } catch (ServiceException e) {
            log.error(
                    "Failed to update project '{}' service type '{}'",
                    projectId,
                    ProjectServiceTypeEnum.getEnum(serviceType));
        }
    }

    /**
     * 截取note中的insuranceCompany对应的公司的损失描述
     * claimNote 要进行操作的字符串
     * insuranceCompany 保险公司id
     */
    private String getLossDescription(
            long projectId, @Nullable String claimNote, Long insuranceCompany) {
        if (Objects.isNull(insuranceCompany)
                || !canGetLossDescription(projectId, insuranceCompany)
                || StringUtils.isEmpty(claimNote)) {
            return Strings.EMPTY;
        }
        var lossDescription = doGetLossDescription(projectId, claimNote, insuranceCompany);
        return Optional.of(lossDescription)
                .filter(Strings::isNotBlank)
                .orElseGet(() -> doGetLossDescriptionFromComment(projectId, insuranceCompany));
    }

    private String doGetLossDescriptionFromComment(long projectId, Long insuranceCompany) {
        if (Objects.isNull(insuranceCompany)
                || !canGetLossDescription(projectId, insuranceCompany)) {
            return Strings.EMPTY;
        }

        var activities =
                activityManager.getActivities(ActivityQuery.builder().projectId(projectId).build());
        return activities.stream()
                .map(Activity::getComment)
                .filter(Objects::nonNull)
                .map(
                        comment ->
                                doGetLossDescription(
                                        projectId, comment.getContent(), insuranceCompany))
                .filter(Strings::isNotBlank)
                .findFirst()
                .orElse(Strings.EMPTY);
    }

    private String doGetLossDescription(
            long projectId, @Nullable String claimNote, Long insuranceCompany) {
        if (StringUtils.isEmpty(claimNote)
                || Objects.isNull(insuranceCompany)
                || !canGetLossDescription(projectId, insuranceCompany)) {
            return Strings.EMPTY;
        }
        // 让正则对象和要作用的字符串相关联。获取匹配器对象,claimNote末尾加上换行符
        var pattern = getLossDescPattern(insuranceCompany);
        Matcher matcher = pattern.matcher(claimNote + "\n\n");
        // 将规则作用到字符串上，并进行符合规则的子串查找
        if (matcher.find()) {
            // 获取匹配后LossDescription组结果
            String res = matcher.group("LossDescription");
            return res.trim();
        }
        return Strings.EMPTY;
    }

    private boolean canGetLossDescription(long projectId, Long insuranceCompany) {
        var project = projectService.getById(projectId);
        if (Objects.isNull(insuranceCompany)
                || Objects.isNull(project)
                || !ProjectServiceTypeEnum.isClaim(project.getServiceType())) {
            return false;
        }
        return Objects.nonNull(getLossDescPattern(insuranceCompany));
    }

    private Pattern getLossDescPattern(long insuranceCompany) {
        // 获取保险公司配置
        var configItem = bees360CompanyConfig.findConfig(insuranceCompany);
        return Optional.ofNullable(configItem)
                .map(Bees360CompanyConfig.CompanyConfigItem::getLossDescExtractRegex)
                .orElse(null);
    }

    /**
     *  查出状态更新时间处于startTime和endTime之间的所有case，并将project_status字段同步到firebase
     *  该方法只用于数据修复
     *
     * @param startTime 状态变更起始时间
     * @param endTime 状态变更结束时间
     */
    @Override
    public void recoverProjectStatusToFirebase(Instant startTime, Instant endTime) {
        var start = startTime.toEpochMilli();
        var end = endTime.toEpochMilli();
        var query = new ProjectFilterQuery();
        query.setProjectStatusTimeStart(start);
        query.setProjectStatusTimeEnd(end);
        var projects = projectMapper.listProjectsWithFilter(query);

        projects.forEach(project -> {
            long projectId = project.getProjectId();
            NewProjectStatusEnum currentStatus = NewProjectStatusEnum.getEnum(project.getProjectStatus());
            updateFirebaseProjectField(project, this::setProjectStatusForRecover);
            log.info(
                "Success Recover firebase project '{}' status to '{} .",
                projectId,
                currentStatus);
        });
    }

    private void setProjectStatusForRecover(Map<String, Object> fields, Project project) {
        fields.put("project_status", project.getProjectStatus());
    }

    /**
     *  insured contact在存入pg时name会做格式化，因此从pg里取最新的同步给firebase
     */
    private void setProjectInsuredName(Map<String, Object> fields, Project project) {
        var contacts = contactManager.findByProjectId(String.valueOf(project.getProjectId()));
        var insured = Iterables.toStream(contacts)
            .filter(c -> StringUtils.equals(c.getRole(), ProjectConstants.ContactRoleType.CONTACT_INSURED))
            .findFirst().orElse(null);

        if(insured == null || StringUtils.isEmpty(insured.getFirstName())) {
            return;
        }

        Optional.ofNullable(insured.getFirstName()).ifPresent(n -> fields.put("insured_first_name", n));
        Optional.ofNullable(insured.getMiddleName()).ifPresent(n -> fields.put("insured_middle_name", n));
        Optional.ofNullable(insured.getLastName()).ifPresent(n -> fields.put("insured_last_name", n));
        Optional.ofNullable(insured.getFullName()).ifPresent(n -> fields.put("insured_name", n));
    }

    private void setContactFields(Map<String, Object> firebaseProjectFields, long projectId) {
        if (!bees360FeatureSwitch.isEnableSyncFirebaseContact()) {
            return;
        }
        var contacts = Iterables.toList(Iterables.transform(contactManager.findByProjectId(String.valueOf(projectId)), this::contactToMap));
        firebaseProjectFields.put("contact", contacts);
    }

    @Override
    public void updateContacts(long projectId, Iterable<? extends Contact> contacts) {
        String documentPath = firebaseService.getProjectDocumentPath(projectId);
        Map<String, Object> fields = new HashMap<>();

        var contactMaps = Iterables.toList(Iterables.transform(contacts, this::contactToMap));
        fields.put("contact", contactMaps);
        setInsuredAndAgentInfo(fields, contacts);
        fields.put("update_time", System.currentTimeMillis());
        log.debug("Success update firebase project '{}' contacts to '{} .", projectId, JsonUtils.toJson(fields));
        firebaseService.syncProjectToFirebaseWithFailedRetry(projectId, documentPath, fields, true);
    }

    @Override
    public void updateParentChildProject(long projectId, String parentProjectId) {
        String documentPath = firebaseService.getProjectDocumentPath(projectId);
        Map<String, Object> fields = new HashMap<>();
        fields.put("parent_project_id", parentProjectId == null ? FieldValue.delete() : parentProjectId);
        firebaseService.syncProjectToFirebaseWithFailedRetry(projectId, documentPath, fields, true);
    }

    private void setInsuredAndAgentInfo(Map<String, Object> fields, Iterable<? extends Contact> contacts) {
        var insured = Iterables.toStream(contacts)
            .filter(c -> StringUtils.equals(c.getRole(), ProjectConstants.ContactRoleType.CONTACT_INSURED))
            .findFirst().orElse(null);

        if (insured != null) {
            fields.put("insured_first_name", insured.getFirstName());
            fields.put("insured_middle_name", insured.getMiddleName());
            fields.put("insured_last_name", insured.getLastName());
            fields.put("insured_name", insured.getFullName());
            fields.put("insured_phone", insured.getPrimaryPhone());
            fields.put("insured_email", insured.getPrimaryEmail());
        }

        var agent = Iterables.toStream(contacts)
            .filter(c -> StringUtils.equals(c.getRole(), ProjectConstants.ContactRoleType.CONTACT_AGENT))
            .findFirst().orElse(null);

        if (agent != null) {
            fields.put("agent_name", agent.getFullName());
            fields.put("agent_phone", agent.getPrimaryPhone());
            fields.put("agent_email", agent.getPrimaryEmail());
        }
    }

    public Map<String, Object> contactToMap(Contact contact) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", contact.getId());
        map.put("role", contact.getRole());
        map.put("full_name", contact.getFullName());
        map.put("first_name", contact.getFirstName());
        map.put("middle_name", contact.getMiddleName());
        map.put("last_name", contact.getLastName());
        map.put("primary_email", contact.getPrimaryEmail());
        map.put("primary_phone", contact.getPrimaryPhone());
        map.put("other_email", contact.getOtherEmail());
        map.put("other_phone", contact.getOtherPhone());
        map.put("is_primary", contact.isPrimary());
        return map;
    }
}
