package com.bees360.internal.ai.service.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Message;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.entity.LogEntry;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectLog;
import com.bees360.internal.ai.entity.dto.ClaimWorkloadStatistics;
import com.bees360.internal.ai.entity.dto.EmailRecipients;
import com.bees360.internal.ai.entity.dto.ProcessorClaimStatistics;
import com.bees360.internal.ai.entity.dto.ProcessorUWStatistics;
import com.bees360.internal.ai.entity.dto.UWWorkloadStatistics;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.LogAiFlowActionEnum;
import com.bees360.internal.ai.entity.enums.LogAiFlowDetailEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogReportActionEnum;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.service.ProjectWorkloadStatisticService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nullable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bees360.activity.Message.ActivityMessage.EntityType.PROJECT;
import static com.bees360.activity.Message.ActivityMessage.EntityType.REPORT;
import static com.bees360.internal.ai.entity.enums.ReportGenerationStatusEnum.APPROVED;
import static com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING;
import static com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum.WHITE_GLOVE;

@Service
@Log4j2
public class ProjectWorkloadStatisticServiceImpl implements ProjectWorkloadStatisticService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private ProjectLogService projectLogEsService;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private EmailRecipients emailRecipients;

    @Autowired
    private Bees360FeatureSwitch bees360FeatureSwitch;

    private final Map<Long, ProjectEsModel> projectEsModelMap = new HashMap<>();

    private static final int COMPLETE_COUNT = 1;
    private static final String HAIL_REPORT_UPLOADED_REGEX = ".*Hail report UPLOADED.*";
    private static final String PROJECTION_TO_SLIDE_1_FIXED_REGEX = ".*Projection to Slide 1 FIXED.*";
    private static final String ANNOTATION_COMPLETED_REGEX = "(?i).*Annotation Completed.*";
    @Value("${bees360.activity-search-days-size:5}")
    private int activitySearchDaysSize;
    @Value("${bees360.es-search-page-size:10000}")
    private int esSearchPageSize;

    @Override
    public Collection<ProcessorUWStatistics> getProcessorUWWorkloadStatistic(
            Instant startTime, Instant endTime) {
        List<? extends Activity> source =
                getProcessorStatisticsSource(startTime, endTime, false).stream()
                        .collect(Collectors.toUnmodifiableList());
        return getProcessorUWWorkloadStatistic(source);
    }

    private List<? extends Activity> filterProject(
            List<? extends Activity> source, boolean isClaim) {
        var projectIds = source.stream().map(Activity::getProjectId).collect(Collectors.toSet());
        var query =
                ProjectEsQueryParam.builder()
                        .projectIds(new ArrayList<>(projectIds))
                        .roleTag("ADMIN")
                        .isClaimSearch(isClaim)
                        .isSearchAll(true)
                        .pageSize(esSearchPageSize)
                        .isContainsDeleted(true)
                        .build();
        var projects = projectEsService.findProjectListByQueryBuild(query);
        for (ProjectEsModel project : projects) {
            projectEsModelMap.put(project.getProjectId(), project);
        }
        var targetProjectIds =
                projects.stream().map(ProjectEsModel::getProjectId).collect(Collectors.toSet());
        source =
                source.stream()
                        .filter(activity -> targetProjectIds.contains(activity.getProjectId()))
                        .collect(Collectors.toList());
        log.info(
                "Start to calculate staff workload of project(isClaim='{}',projects='{}').",
                isClaim,
                targetProjectIds);
        return source;
    }

    @Override
    public Collection<ProcessorClaimStatistics> getProcessorClaimWorkloadStatistic(
            Instant startTime, Instant endTime) {
        List<? extends Activity> source =
                getProcessorStatisticsSource(startTime, endTime, true).stream()
                        .collect(Collectors.toUnmodifiableList());
        return getProcessorClaimStatistics(source, startTime, endTime);
    }

    @Override
    public Collection<ClaimWorkloadStatistics> getAdjusterWorkloadStatistic(Instant startTime, Instant endTime) {
        return getAdjusterWorkloadStatistics(startTime, endTime);
    }

    @Override
    public Collection<UWWorkloadStatistics> getReviewerUWWorkloadStatistic(Instant startTime, Instant endTime) {
        Set<Long> reviewerProjectIds = getReviewerClientProjectId(startTime, endTime);
        return getReviwerUWorkloadStatistics(reviewerProjectIds);
    }

    @Override
    public Collection<ClaimWorkloadStatistics> getReviewerClaimWorkloadStatistic(Instant startTime, Instant endTime) {
        Set<Long> reviewerProjectIds = getReviewerClientProjectId(startTime, endTime);
        return getReviewerClaimWorkloadStatistics(reviewerProjectIds,true);
    }

    private List<? extends Activity> getProcessorStatisticsSource(
            Instant startTime, Instant endTime, boolean isClaim) {
        var daysApart =
                Duration.ofMillis(endTime.toEpochMilli() - startTime.toEpochMilli()).toDays();
        if (daysApart <= activitySearchDaysSize) {
            var source = activityManager.getActivities(
                    ActivityQuery.builder()
                            .entityType(Message.ActivityMessage.EntityType.REPORT.name())
                            .fieldName(Message.ActivityMessage.FieldName.STATUS.name())
                            .createdAtStart(startTime)
                            .createdAtEnd(endTime)
                            .build());
            return filterProject(source, isClaim);
        }

        var splitTime = startTime.plus(activitySearchDaysSize, ChronoUnit.DAYS);
        var stream1 = getProcessorStatisticsSource(startTime, splitTime, isClaim);
        var stream2 = getProcessorStatisticsSource(splitTime, endTime, isClaim);
        return Stream.concat(stream1.stream(), stream2.stream()).collect(Collectors.toList());
    }

    private Collection<ProcessorUWStatistics> getProcessorUWWorkloadStatistic(List<? extends Activity> source){
        // 获取生成报告的统计数据
        var statisticalP4pServiceTypes =
                List.of(PREMIUM_FOUR_POINT_UNDERWRITING.getCode(), WHITE_GLOVE.getCode());
        Stream<ProcessorUWStatistics> generatedStream =
            filterFirstPriorityGenerated(source)
                .map(
                    activity ->
                    {
                        String companyName = getCompanyName(activity.getProjectId());
                        User user = getUser(activity.getCreatedBy());
                        var userName = user.getName();
                        var userEmail = user.getEmail();
                        if (isSpecificServiceType(activity.getProjectId(), statisticalP4pServiceTypes)) {
                            return ProcessorUWStatistics.newBuilder()
                                .setCompanyName(companyName)
                                .setStaffName(userName)
                                .setStaffEmail(userEmail)
                                .setGeneratedPremium(1)
                                .setGeneratedPremiumProjects(activity.getProjectId() + "")
                                .build();
                        }

                        return ProcessorUWStatistics.newBuilder()
                            .setCompanyName(companyName)
                            .setStaffName(userName)
                            .setStaffEmail(userEmail)
                            .setGenerated(1)
                            .setGeneratedProjects(activity.getProjectId() + "")
                            .build();
                    });

        // 获取提交报告的统计数据
        Stream<ProcessorUWStatistics> submittedStream =
            filterLastPrioritySubmitted(source)
                .map(
                    activity -> {
                        String companyName = getCompanyName(activity.getProjectId());
                        User user = getUser(activity.getCreatedBy());
                        var userName = user.getName();
                        var userEmail = user.getEmail();
                        if (isSpecificServiceType(activity.getProjectId(), statisticalP4pServiceTypes))  {
                            return ProcessorUWStatistics.newBuilder()
                                .setCompanyName(companyName)
                                .setStaffName(userName)
                                .setStaffEmail(userEmail)
                                .setSubmittedPremium(1)
                                .setSubmittedPremiumProjects(activity.getProjectId() + "")
                                .build();
                        }

                        return ProcessorUWStatistics.newBuilder()
                            .setCompanyName(companyName)
                            .setStaffName(userName)
                            .setStaffEmail(userEmail)
                            .setSubmitted(1)
                            .setSubmittedProjects(activity.getProjectId() + "")
                            .build();
                    });

        // 获取提交报告的统计数据
        Stream<ProcessorUWStatistics> approvedStream =
            filterLastPriorityApproved(source)
                .map(
                    activity -> {
                        String companyName = getCompanyName(activity.getProjectId());
                        User user = getUser(activity.getCreatedBy());
                        var userName = user.getName();
                        var userEmail = user.getEmail();
                        if (isSpecificServiceType(activity.getProjectId(), statisticalP4pServiceTypes))  {
                            return ProcessorUWStatistics.newBuilder()
                                .setCompanyName(companyName)
                                .setStaffName(userName)
                                .setStaffEmail(userEmail)
                                .setApprovedPremium(1)
                                .setApprovedPremiumProjects(activity.getProjectId() + "")
                                .build();
                        }
                        return ProcessorUWStatistics.newBuilder()
                            .setCompanyName(companyName)
                            .setStaffName(userName)
                            .setStaffEmail(userEmail)
                            .setApproved(1)
                            .setApprovedProjects(activity.getProjectId() + "")
                            .build();
                    });

        var stream = Stream.of(generatedStream, submittedStream, approvedStream).flatMap(Function.identity());
        var processorUWStatistics = reduceUW(stream)
            .sorted(Comparator.comparing(ProcessorUWStatistics::getStaffName))
            .collect(Collectors.toList());
        if (!bees360FeatureSwitch.isEnableNoCommasPadding()) {
            processorUWStatistics.forEach(
                    statistic -> {
                        if (statistic.getGenerated() > 0) {
                            var premiumProjects = statistic.getGeneratedPremiumProjects();
                            Optional.ofNullable(premiumProjects)
                                    .ifPresent(s -> statistic.setGeneratedPremiumProjects("," + s));
                        }
                        if (statistic.getSubmitted() > 0) {
                            var premiumProjects = statistic.getSubmittedPremiumProjects();
                            Optional.ofNullable(premiumProjects)
                                    .ifPresent(s -> statistic.setSubmittedPremiumProjects("," + s));
                        }
                        if (statistic.getApproved() > 0) {
                            var premiumProjects = statistic.getApprovedPremiumProjects();
                            Optional.ofNullable(premiumProjects)
                                    .ifPresent(s -> statistic.setApprovedPremiumProjects("," + s));
                        }
                    });
        }
        return processorUWStatistics;
    }

    private User getUser(String userId) {
        try {
            return userProvider.getUser(userId);
        } catch (NoSuchElementException e) {
            log.warn(
                "Failed to get user '{}' when try to send processor workload email, using userId as userName.",
                userId);
            return User.from(com.bees360.user.Message.UserMessage.
                    newBuilder()
                    .setId(userId)
                    .setName(userId)
                    .build());
        }
    }

    private String getCompanyName(long projectId) {
        var project =
                Optional.ofNullable(projectEsModelMap.get(projectId))
                        .orElseGet(() -> projectEsService.findProjectByProjectId(projectId));
        return Optional.ofNullable(project).map(ProjectEsModel::getInsuranceCompanyName).orElse("");
    }

    /** whether the user has role of {@code UserAuthEnum.ROLE_PROCESSOR} and @<code>ROLE_PRODUCER</code> */
    private boolean isProcessor(String userId) {
        try {
            return Optional.ofNullable(userProvider.getUser(userId))
                    .map(
                            user ->
                                    UserAuthEnum.hasRole(
                                                    user.getAllAuthority(),
                                                    UserAuthEnum.ROLE_PROCESSOR)
                                            || UserAuthEnum.hasRole(
                                                    user.getAllAuthority(),
                                                    UserAuthEnum.ROLE_PRODUCER))
                    .orElse(false);
        } catch (NoSuchElementException e) {
            log.warn("Failed to get user '{}' when try to send staff workload email, assuming it is processor.", userId);
            return true;
        }
    }

    private boolean isSpecificServiceType(long projectId, List<Integer> serviceTypeCodes) {
        var project =
                Optional.ofNullable(projectEsModelMap.get(projectId))
                        .orElseGet(() -> projectEsService.findProjectByProjectId(projectId));
        return Optional.ofNullable(project)
                .map(ProjectEsModel::getServiceType)
                .map(serviceTypeCodes::contains)
                .orElse(false);
    }

    private Collection<ProcessorClaimStatistics> getProcessorClaimStatistics(
        List<? extends Activity> source, Instant startTime, Instant endTime) {
        // 获取提交报告的统计数据
        Stream<ProcessorClaimStatistics> darSubmitStream =
            filterFirstPrioritySubmitted(
                source, ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT)
                .map(
                                activity -> {
                                    User user = getUser(activity.getCreatedBy());
                                    var userName = user.getName();
                                    var userEmail = user.getEmail();
                                    return ProcessorClaimStatistics.newBuilder()
                                            .setCompanyName(getCompanyName(activity.getProjectId()))
                                            .setStaffName(userName)
                                            .setStaffEmail(userEmail)
                                            .setDarSubmitted(COMPLETE_COUNT)
                                            .setDarSubmittedProjects(
                                                    String.valueOf(activity.getProjectId()))
                                            .build();
                    });

        // 获取提交报告的统计数据
        Stream<ProcessorClaimStatistics> pirSubmitStream =
            filterFirstPrioritySubmitted(source, ReportTypeEnum.PROPERTY_IMAGE_REPORT)
                        .map(
                                activity -> {
                                    User user = getUser(activity.getCreatedBy());
                                    var userName = user.getName();
                                    var userEmail = user.getEmail();
                                    return ProcessorClaimStatistics.newBuilder()
                                            .setCompanyName(getCompanyName(activity.getProjectId()))
                                            .setStaffName(userName)
                                            .setStaffEmail(userEmail)
                                            .setPirSubmitted(COMPLETE_COUNT)
                                            .setPirSubmittedProjects(
                                                    String.valueOf(activity.getProjectId()))
                                            .build();
                                });

        Stream<ProcessorClaimStatistics> darApproveStream =
                filterLastPriorityApproved(source, ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT)
                        .map(
                                activity -> {
                                    User user = getUser(activity.getCreatedBy());
                                    var userName = user.getName();
                                    var userEmail = user.getEmail();
                                    return ProcessorClaimStatistics.newBuilder()
                                            .setCompanyName(getCompanyName(activity.getProjectId()))
                                            .setStaffName(userName)
                                            .setStaffEmail(userEmail)
                                            .setDarApproved(COMPLETE_COUNT)
                                            .setDarApprovedProjects(
                                                    String.valueOf(activity.getProjectId()))
                                            .build();
                                });

        Stream<ProcessorClaimStatistics> pirApproveStream =
                filterLastPriorityApproved(source, ReportTypeEnum.PROPERTY_IMAGE_REPORT)
                        .map(
                                activity -> {
                                    User user = getUser(activity.getCreatedBy());
                                    var userName = user.getName();
                                    var userEmail = user.getEmail();
                                    return ProcessorClaimStatistics.newBuilder()
                                            .setCompanyName(getCompanyName(activity.getProjectId()))
                                            .setStaffName(userName)
                                            .setStaffEmail(userEmail)
                                            .setPirApproved(COMPLETE_COUNT)
                                            .setPirApprovedProjects(
                                                    String.valueOf(activity.getProjectId()))
                                            .build();
                                });

        // 3d statistics
        Set<Long> projectIds =
            source.stream().map(Activity::getProjectId).collect(Collectors.toSet());
        Stream<ProcessorClaimStatistics> threeDStatsStream =
            projectLogEsService.getLogList(projectIds).stream()
                .map(
                    projectLog -> {
                        long projectId = projectLog.getProjectId();
                        LogEntry earliestBoundaryLog =
                            findEarliestBoundaryLog(projectLog, startTime, endTime);
                        if (earliestBoundaryLog == null) {
                            return null;
                        }
                        User user = getUser(earliestBoundaryLog.getUserId());
                        var userEmail = user.getEmail();
                        var userName = user.getName();
                        return ProcessorClaimStatistics.newBuilder()
                            .setCompanyName(getCompanyName(projectId))
                            .setStaffName(userName)
                            .setThreeDSubmitted(COMPLETE_COUNT)
                            .setThreeDSubmittedProjects(
                                String.valueOf(projectId))
                            .setStaffEmail(userEmail)
                            .build();
                    })
                .filter(Objects::nonNull);

        var hailReportUploaded =
           lastPriority(getHailReportUploadedAc(startTime, endTime).stream())
               .map(this::mapByHailReportUploaded);
        var projectionFixed =
            lastPriority(getProjectionFixedAc(startTime, endTime).stream())
                .map(this::mapByProjectionFixedAc);
        var annotationCompletedStream =
            firstPriority(getAnnotationCompletedAc(startTime, endTime).stream())
                .map(this::mapByAnnotationCompletedAc);

        var stream =
            Stream.of(
                    darSubmitStream,
                    darApproveStream,
                    pirSubmitStream,
                    pirApproveStream,
                    threeDStatsStream,
                    hailReportUploaded,
                    projectionFixed,
                    annotationCompletedStream)
                .flatMap(Function.identity());
        return reduceClaim(stream)
            .sorted(Comparator.comparing(ProcessorClaimStatistics::getStaffName))
            .collect(Collectors.toList());
    }

    private ProcessorClaimStatistics mapByAnnotationCompletedAc(Activity activity) {
        User user = getUser(activity.getCreatedBy());
        var userName = user.getName();
        var userEmail = user.getEmail();
        return ProcessorClaimStatistics.newBuilder()
            .setAnnotationCompleted(COMPLETE_COUNT)
            .setAnnotationCompletedProjects(String.valueOf(activity.getProjectId()))
            .setCompanyName(getCompanyName(activity.getProjectId()))
            .setStaffName(userName)
            .setStaffEmail(userEmail)
            .build();
    }

    private ProcessorClaimStatistics mapByHailReportUploaded(Activity activity) {
        User user = getUser(activity.getCreatedBy());
        var userName = user.getName();
        var userEmail = user.getEmail();
        return ProcessorClaimStatistics.newBuilder()
                .setHailReportUploaded(COMPLETE_COUNT)
                .setHailReportUploadedProjects(String.valueOf(activity.getProjectId()))
                .setCompanyName(getCompanyName(activity.getProjectId()))
                .setStaffName(userName)
                .setStaffEmail(userEmail)
                .build();
    }

    private ProcessorClaimStatistics mapByProjectionFixedAc(Activity activity) {
        User user = getUser(activity.getCreatedBy());
        var userName = user.getName();
        var userEmail = user.getEmail();
        return ProcessorClaimStatistics.newBuilder()
                .setProjectionFixed(COMPLETE_COUNT)
                .setProjectionFixedProjects(String.valueOf(activity.getProjectId()))
                .setCompanyName(getCompanyName(activity.getProjectId()))
                .setStaffEmail(userEmail)
                .setStaffName(userName)
                .build();
    }

    private Stream<? extends Activity> filterFirstPriorityGenerated(
        List<? extends Activity> source) {
        Predicate<Activity> filter = activity -> isProcessor(activity.getCreatedBy());
        return getReports(source, LogReportActionEnum.GENERATED)
            .filter(filter)
            .collect(
                Collectors.toMap(
                    Activity::getProjectId,
                    activity -> activity,
                    (a1, a2) ->
                        // 取最先生成的报告
                        a1.getCreatedAt().isBefore(a2.getCreatedAt())
                            ? a1
                            : a2))
            .values()
            .stream();
    }

    private Stream<? extends Activity> filterFirstPrioritySubmitted(
        List<? extends Activity> source, ReportTypeEnum typeEnum) {
        Predicate<Activity> filter =
            activity ->
                Objects.equals(String.valueOf(typeEnum.getCode()), activity.getEntityId())
                    && isProcessor(activity.getCreatedBy());
        return getReports(source, LogReportActionEnum.SUBMITTED)
            .filter(filter)
            .collect(
                Collectors.toMap(
                    Activity::getProjectId,
                    activity -> activity,
                    (a1, a2) ->
                        // 取最先提交的报告
                        a1.getCreatedAt().isBefore(a2.getCreatedAt())
                            ? a1
                            : a2))
            .values()
            .stream();
    }

    private Stream<? extends Activity> filterLastPrioritySubmitted(
        List<? extends Activity> source) {
        Predicate<Activity> filter = activity -> isProcessor(activity.getCreatedBy());
        return getReports(source, LogReportActionEnum.SUBMITTED)
            .filter(filter)
            .collect(
                Collectors.toMap(
                    Activity::getProjectId,
                    activity -> activity,
                    (a1, a2) ->
                        // 取最后提交的报告
                        a1.getCreatedAt().isAfter(a2.getCreatedAt()) ? a1 : a2))
            .values()
            .stream();
    }

    private Stream<? extends Activity> filterLastPriorityApproved(List<? extends Activity> source) {
        Predicate<Activity> filter = activity -> isProcessor(activity.getCreatedBy());
        return lastPriority(getReports(source, LogReportActionEnum.APPROVED).filter(filter));
    }

    private Stream<? extends Activity> filterLastPriorityApproved(
            List<? extends Activity> source, ReportTypeEnum typeEnum) {
        Predicate<Activity> filter =
                activity ->
                        Objects.equals(String.valueOf(typeEnum.getCode()), activity.getEntityId())
                                && isProcessor(activity.getCreatedBy());
        return lastPriority(getReports(source, LogReportActionEnum.APPROVED).filter(filter));
    }

    private Stream<? extends Activity> lastPriority(Stream<? extends Activity> source) {
        return source
                .collect(
                        Collectors.toMap(
                                Activity::getProjectId,
                                activity -> activity,
                                (a1, a2) ->
                                        // 取最后生成的报告
                                        a1.getCreatedAt().isAfter(a2.getCreatedAt()) ? a1 : a2))
                .values()
                .stream();
    }

    private Stream<? extends Activity> firstPriority(Stream<? extends Activity> source) {
        return source
                .collect(
                        Collectors.toMap(
                                Activity::getProjectId,
                                activity -> activity,
                                (a1, a2) ->
                                        // 取项目中的第一条activity
                                        a1.getCreatedAt().isBefore(a2.getCreatedAt()) ? a1 : a2))
                .values()
                .stream();
    }

    private @Nullable
    String concat(@Nullable String s1, @Nullable String s2) {
        return Stream.of(s1, s2)
            .filter(StringUtils::isNotBlank)
            .collect(
                Collectors.collectingAndThen(
                    Collectors.joining(","), c -> !c.isEmpty() ? c : null));
    }

    private ProcessorClaimStatistics reduceTwo(
        ProcessorClaimStatistics p1, ProcessorClaimStatistics p2) {
        log.info("P1 is " + p1.getCompanyName() + p1.getStaffName());
        var darSubmittedProjects =
            concat(p1.getDarSubmittedProjects(), p2.getDarSubmittedProjects());
        var pirSubmittedProjects =
            concat(p1.getPirSubmittedProjects(), p2.getPirSubmittedProjects());
        var threeDSubmittedProjects =
            concat(p1.getThreeDSubmittedProjects(), p2.getThreeDSubmittedProjects());
        var darApprovedProjects =
            concat(p1.getDarApprovedProjects(), p2.getDarApprovedProjects());
        var pirApprovedProjects =
            concat(p1.getPirApprovedProjects(), p2.getPirApprovedProjects());
        var hailReportUploadedProjects =
            concat(p1.getHailReportUploadedProjects(), p2.getHailReportUploadedProjects());
        var projectionFixedProjects =
            concat(p1.getProjectionFixedProjects(), p2.getProjectionFixedProjects());
        var annotationCompletedProjects =
            concat(p1.getAnnotationCompletedProjects(), p2.getAnnotationCompletedProjects());
        return ProcessorClaimStatistics.newBuilder()
            .setCompanyName(p1.getCompanyName())
            .setStaffName(p1.getStaffName())
            .setStaffEmail(p1.getStaffEmail())
            .setDarSubmitted(p1.getDarSubmitted() + p2.getDarSubmitted())
            .setDarSubmittedProjects(darSubmittedProjects)
            .setPirSubmitted(p1.getPirSubmitted() + p2.getPirSubmitted())
            .setPirSubmittedProjects(pirSubmittedProjects)
            .setThreeDSubmitted(p1.getThreeDSubmitted() + p2.getThreeDSubmitted())
            .setThreeDSubmittedProjects(threeDSubmittedProjects)
            .setDarApproved(p1.getDarApproved() + p2.getDarApproved())
            .setDarApprovedProjects(darApprovedProjects)
            .setPirApproved(p1.getPirApproved() + p2.getPirApproved())
            .setPirApprovedProjects(pirApprovedProjects)
            .setHailReportUploaded(p1.getHailReportUploaded() + p2.getHailReportUploaded())
            .setHailReportUploadedProjects(hailReportUploadedProjects)
            .setProjectionFixed(p1.getProjectionFixed() + p2.getProjectionFixed())
            .setProjectionFixedProjects(projectionFixedProjects)
            .setAnnotationCompleted(p1.getAnnotationCompleted() + p2.getAnnotationCompleted())
            .setAnnotationCompletedProjects(annotationCompletedProjects)
            .build();
    }

    private ProcessorUWStatistics reduceTwo(
        ProcessorUWStatistics p1, ProcessorUWStatistics p2) {
        return ProcessorUWStatistics.newBuilder()
            .setCompanyName(p1.getCompanyName())
            .setStaffName(p1.getStaffName())
            .setStaffEmail(p1.getStaffEmail())
            .setGenerated(p1.getGenerated() + p2.getGenerated())
            .setGeneratedPremium(p1.getGeneratedPremium() + p2.getGeneratedPremium())
            .setGeneratedProjects(concat(p1.getGeneratedProjects(), p2.getGeneratedProjects()))
            .setGeneratedPremiumProjects(concat(p1.getGeneratedPremiumProjects(), p2.getGeneratedPremiumProjects()))
            .setSubmitted(p1.getSubmitted() + p2.getSubmitted())
            .setSubmittedPremium(p1.getSubmittedPremium() + p2.getSubmittedPremium())
            .setSubmittedProjects(concat(p1.getSubmittedProjects(), p2.getSubmittedProjects()))
            .setSubmittedPremiumProjects(concat(p1.getSubmittedPremiumProjects(), p2.getSubmittedPremiumProjects()))
            .setApproved(p1.getApproved() + p2.getApproved())
            .setApprovedPremium(p1.getApprovedPremium() + p2.getApprovedPremium())
            .setApprovedProjects(concat(p1.getApprovedProjects(), p2.getApprovedProjects()))
            .setApprovedPremiumProjects(concat(p1.getApprovedPremiumProjects(), p2.getApprovedPremiumProjects()))
            .build();
    }

    private Stream<ProcessorClaimStatistics> reduceClaim(
        Stream<ProcessorClaimStatistics> stream) {
        return stream
            .collect(
                Collectors.toMap(
                    p -> p.getCompanyName() + p.getStaffName(),
                    p -> p,
                    this::reduceTwo))
            .values()
            .stream();
    }

    private Stream<ProcessorUWStatistics> reduceUW(Stream<ProcessorUWStatistics> stream) {
        return stream
            .collect(
                Collectors.toMap(
                    p -> p.getCompanyName() + p.getStaffName(),
                    p -> p,
                    this::reduceTwo))
            .values()
            .stream();
    }

    private LogEntry findEarliestBoundaryLog(ProjectLog projectLog, Instant startTime, Instant endTime) {
        List<LogEntry> logEntries = projectLog.getLogEntry();
        return logEntries.stream()
            .filter(
                logEntry ->
                    LogEntryTypeEnum.PROJECT_AI_FLOW
                        .getType()
                        .equals(logEntry.getType()))
            .filter(
                logEntry ->
                    Integer.valueOf(LogAiFlowDetailEnum.BOUNDARY.getCode())
                        .equals(logEntry.getLogDetail().getDetailCode())
                        && Integer.valueOf(
                            LogAiFlowActionEnum.FINISHED.getCode())
                        .equals(
                            logEntry.getLogDetail()
                                .getActionCode()))
            .filter(
                logEntry -> {
                    String createdTime = logEntry.getCreatedTime();
                    if (StringUtils.isBlank(createdTime)) {
                        return false;
                    }
                    long millis = dateStrToLong(createdTime);
                    return millis >= startTime.toEpochMilli()
                               && millis <= endTime.toEpochMilli();
                })
            .min(Comparator.comparingLong(log -> dateStrToLong(log.getCreatedTime())))
            .orElse(null);
    }

    private long dateStrToLong(String dateString) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        try {
            return sdf.parse(dateString).getTime();
        } catch (ParseException e) {
            log.error("Date string " + dateString + " can't be converted to date", e);
            return Long.MAX_VALUE;
        }
    }

    private Stream<? extends Activity> getReports(
        List<? extends Activity> source, LogReportActionEnum action) {
        return source.stream()
            .filter(activity -> action.getValue().equals(activity.getValue()));
    }

    private String getAdjusterUserName(String userId) {
        try {
            return Optional.ofNullable(userProvider.getUser(userId))
                .filter(
                    user ->
                        UserAuthEnum.hasRole(
                            user.getAllAuthority(), UserAuthEnum.ROLE_ADJUSTER))
                .map(User::getName)
                .orElse(null);
        } catch (NoSuchElementException e) {
            log.warn("Failed to get user '{}' when try to send staff workload email, using userId as userName.", userId);
            return userId;
        }
    }

    /**
     * 用户是否具有Adjuster角色，如果配置{@link EmailRecipients#getAdjusterExclude()}中有该用户，则返回False
     *
     * @param userId 用户ID
     * @return 用户是否有Adjuster角色，并且没有被排除统计
     */
    private boolean isAdjuster(String userId) {
        try {
            return !emailRecipients.getAdjusterExclude().contains(userId)
                       && Objects.nonNull(getAdjusterUserName(userId));
        } catch (NoSuchElementException e) {
            log.warn("Failed to get user '{}' when try to send staff workload email, assuming it is adjuster.", userId);
            return true;
        }
    }

    private Collection<ClaimWorkloadStatistics> getAdjusterWorkloadStatistics(Instant startTime, Instant endTime) {
        Predicate<Activity> filter = activity -> isAdjuster(activity.getCreatedBy());
        Stream<ClaimWorkloadStatistics> estimates =
            getStatusChangedToEstimateActivities(startTime, endTime).stream()
                .filter(filter)
                // 通过projectId分组, 只取第一条activity作为有效的activity
                .collect(
                    Collectors.toMap(
                        Activity::getProjectId,
                        activity -> activity,
                        (a1, a2) ->
                            a1.getCreatedAt().isBefore(a2.getCreatedAt())
                                ? a1
                                : a2))
                .values()
                .stream()
                .map(
                    activity -> {
                        String adjusterName =
                            getAdjusterUserName(activity.getCreatedBy());
                        return new ClaimWorkloadStatistics(
                            activity.getCreatedBy(),
                            adjusterName,
                            0,
                            1,
                            0,
                            activity.getProjectId());
                    });
        Stream<ClaimWorkloadStatistics> dars =
            getDARReportActivities(startTime, endTime).stream()
                .filter(filter)
                // 通过projectId分组, 只取第一条activity作为有效的activity
                .collect(
                    Collectors.toMap(
                        Activity::getProjectId,
                        activity -> activity,
                        (a1, a2) ->
                            a1.getCreatedAt().isBefore(a2.getCreatedAt())
                                ? a1
                                : a2))
                .values()
                .stream()
                .map(
                    activity -> {
                        String adjusterName =
                            getAdjusterUserName(activity.getCreatedBy());
                        return new ClaimWorkloadStatistics(
                            activity.getCreatedBy(),
                            adjusterName,
                            1,
                            0,
                            0,
                            activity.getProjectId());
                    });
        return new HashSet<>(
            Stream.concat(estimates, dars)
                .collect(
                    // 相同的员工相同的project应该聚合成一条数据
                    Collectors.toMap(
                        w ->
                            String.join(
                                "_",
                                w.getStaffId(),
                                String.valueOf(w.getProjectId())),
                        w -> w,
                        (s1, s2) ->
                            new ClaimWorkloadStatistics(
                                s1.getStaffId(),
                                s1.getStaffName(),
                                s1.getDarCount() + s2.getDarCount(),
                                s1.getEstimateCount()
                                    + s2.getEstimateCount(),
                                s1.getThreeDCount()
                                    + s2.getThreeDCount(),
                                s1.getProjectId())))
                .values());
    }

    private List<? extends Activity> getStatusChangedToEstimateActivities(Instant startTime, Instant endTime) {
        var daysApart =
            Duration.ofMillis(endTime.toEpochMilli() - startTime.toEpochMilli()).toDays();
        if (daysApart <= activitySearchDaysSize) {
            return activityManager.getActivities(
                ActivityQuery.builder()
                    .entityType(PROJECT.name())
                    .fieldName(Message.ActivityMessage.FieldName.STATUS.name())
                    .value(AiProjectStatusEnum.ESTIMATE_COMPLETED.getDisplay())
                    .createdAtStart(startTime)
                    .createdAtEnd(endTime)
                    .build());
        }

        var splitTime = startTime.plus(activitySearchDaysSize, ChronoUnit.DAYS);
        var stream1 = getStatusChangedToEstimateActivities(startTime, splitTime);
        var stream2 = getStatusChangedToEstimateActivities(splitTime, endTime);
        return Stream.concat(stream1.stream(), stream2.stream()).collect(Collectors.toList());
    }

    private List<? extends Activity> getDARReportActivities(Instant startTime, Instant endTime) {
        var daysApart =
            Duration.ofMillis(endTime.toEpochMilli() - startTime.toEpochMilli()).toDays();
        if (daysApart <= activitySearchDaysSize) {
            return activityManager.getActivities(
                ActivityQuery.builder()
                    .entityType(Message.ActivityMessage.EntityType.REPORT.name())
                    .entityId(
                        String.valueOf(
                            com.bees360.internal.ai.entity.enums.ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT
                                .getCode()))
                    .fieldName(Message.ActivityMessage.FieldName.STATUS.name())
                    .value(
                        "^"
                            + LogReportActionEnum.SUBMITTED.getValue()
                            + "|"
                            + LogReportActionEnum.APPROVED.getValue()
                            + "$")
                    .createdAtStart(startTime)
                    .createdAtEnd(endTime)
                    .build());
        }

        var splitTime = startTime.plus(activitySearchDaysSize, ChronoUnit.DAYS);
        var stream1 = getDARReportActivities(startTime, splitTime);
        var stream2 = getDARReportActivities(splitTime, endTime);
        return Stream.concat(stream1.stream(), stream2.stream()).collect(Collectors.toList());
    }

    private List<? extends Activity> getReportApprovedActivities(
            Instant startTime, Instant endTime) {
        var daysApart =
                Duration.ofMillis(endTime.toEpochMilli() - startTime.toEpochMilli()).toDays();
        if (daysApart <= activitySearchDaysSize) {
            return activityManager.getActivities(
                    ActivityQuery.builder()
                            .entityType(REPORT.name())
                            .fieldName(Message.ActivityMessage.FieldName.STATUS.name())
                            .value("^" + APPROVED.getDisplay() + "$")
                            .createdAtStart(startTime)
                            .createdAtEnd(endTime)
                            .build());
        }

        var splitTime = startTime.plus(activitySearchDaysSize, ChronoUnit.DAYS);
        var stream1 = getReportApprovedActivities(startTime, splitTime);
        var stream2 = getReportApprovedActivities(splitTime, endTime);
        return Stream.concat(stream1.stream(), stream2.stream()).collect(Collectors.toList());
    }

    private List<? extends Activity> getHailReportUploadedAc(Instant startTime, Instant endTime) {
        return getActivitiesByTagNameRegex(startTime, endTime, HAIL_REPORT_UPLOADED_REGEX);
    }

    private List<? extends Activity> getProjectionFixedAc(Instant startTime, Instant endTime) {
        return getActivitiesByTagNameRegex(startTime, endTime, PROJECTION_TO_SLIDE_1_FIXED_REGEX);
    }

    private List<? extends Activity> getAnnotationCompletedAc(Instant startTime, Instant endTime) {
        return getActivitiesByTagNameRegex(startTime, endTime, ANNOTATION_COMPLETED_REGEX);
    }

    private List<? extends Activity> getActivitiesByTagNameRegex(Instant startTime, Instant endTime, String tagNameRegex) {
        var daysApart =
            Duration.ofMillis(endTime.toEpochMilli() - startTime.toEpochMilli()).toDays();
        if (daysApart <= activitySearchDaysSize) {
            var query =
                ActivityQuery.builder()
                    .action(Message.ActivityMessage.ActionType.CREATE.name())
                    .entityType(PROJECT.name())
                    .fieldName(Message.ActivityMessage.FieldName.TAG.name())
                    .value(tagNameRegex)
                    .createdAtStart(startTime)
                    .createdAtEnd(endTime)
                    .build();
            return activityManager.getActivities(query);
        }

        var splitTime = startTime.plus(activitySearchDaysSize, ChronoUnit.DAYS);
        var stream1 = getActivitiesByTagNameRegex(startTime, splitTime, tagNameRegex);
        var stream2 = getActivitiesByTagNameRegex(splitTime, endTime, tagNameRegex);
        return Stream.concat(stream1.stream(), stream2.stream()).collect(Collectors.toList());
    }

    private Set<Long> getReviewerClientProjectId(Instant startTime, Instant endTime) {
        // 得到对应时间内所有Report approved项目的统计
        List<? extends Activity> activities = getReportApprovedActivities(startTime, endTime);
        // Return_To_Client 或者 Client_Received 的项目
        return activities.stream().map(Activity::getProjectId).collect(Collectors.toUnmodifiableSet());
    }

    private List<UWWorkloadStatistics> getReviwerUWorkloadStatistics(Set<Long> projectIds) {
        final List<ProjectEsModel> esProjects = getReviewerESProjects(projectIds, false);

        final HashMap<String, Map<Long, UWWorkloadStatistics>> map = new HashMap<>();
        esProjects.forEach(
            project -> {
                final int serviceType = project.getServiceType();
                final ProjectServiceTypeEnum serviceTypeEnum =
                    ProjectServiceTypeEnum.getEnum(serviceType);
                if (serviceTypeEnum == null) {
                    return;
                }
                final Optional<MemberInfo> reviewOp =
                    project.getMembers().stream()
                        .filter(mem -> UserAuthEnum.ROLE_REVIEWER.getAuth().equals(mem.getAuth()))
                        .findFirst();
                if (reviewOp.isEmpty()) {
                    return;
                }
                String reviewerId = reviewOp.get().getId();
                String name = reviewOp.get().getName();
                final Long insuranceCompanyId = project.getInsuranceCompany();
                final String insuranceCompanyName = project.getInsuranceCompanyName();
                if (StringUtils.isBlank(reviewerId)
                    || StringUtils.isBlank(name)
                    || insuranceCompanyId == null
                    || StringUtils.isBlank(insuranceCompanyName)) {
                    return;
                }

                Map<Long, UWWorkloadStatistics> statsMap =
                    map.computeIfAbsent(reviewerId, k -> new HashMap<>());
                final UWWorkloadStatistics stats =
                    statsMap.computeIfAbsent(
                        insuranceCompanyId,
                        k -> {
                            final UWWorkloadStatistics _stats = new UWWorkloadStatistics();
                            _stats.setStaffId(reviewerId);
                            _stats.setStaffName(name);
                            _stats.setInsuredCompanyId(insuranceCompanyId);
                            _stats.setInsuredCompanyName(insuranceCompanyName);
                            return _stats;
                        });
                reviewerSumUp(serviceTypeEnum, stats);
            });
        return map.values().stream()
            .map(Map::values)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    }

    private List<ProjectEsModel> getReviewerESProjects(Set<Long> projectIds, boolean isClaim) {
        ProjectEsQueryParam param =
            ProjectEsQueryParam.builder()
                .roleTag("ADMIN")
                .isSearchAll(true)
                .projectIds(List.copyOf(projectIds))
                .memberRole(UserAuthEnum.ROLE_REVIEWER.getAuth())
                .isClaimSearch(isClaim)
                .build();
        return projectEsService.findProjectListByQueryBuild(param);
    }

    private void reviewerSumUp(ProjectServiceTypeEnum serviceTypeEnum, UWWorkloadStatistics stats) {
        if (ProjectServiceTypeEnum.ROOF_ONLY_UNDERWRITING.equals(serviceTypeEnum)) {
            stats.getRoofOnly().incrementAndGet();
        }
        if (ProjectServiceTypeEnum.EXTERIOR_UNDERWRITING.equals(serviceTypeEnum)) {
            stats.getExterior().incrementAndGet();
        }
        if (ProjectServiceTypeEnum.FOUR_POINT_SELF_UNDERWRITING.equals(serviceTypeEnum)
            || ProjectServiceTypeEnum.FOUR_POINT_UNDERWRITING.equals(serviceTypeEnum)) {
            stats.getFourPoint().incrementAndGet();
        }
        if (PREMIUM_FOUR_POINT_UNDERWRITING.equals(serviceTypeEnum)) {
            stats.getPremiumFourPoint().incrementAndGet();
        }
        stats.getTotal().incrementAndGet();
    }

    private Collection<ClaimWorkloadStatistics> getReviewerClaimWorkloadStatistics(
        Set<Long> projectIds, boolean isClaim) {

        final List<ProjectEsModel> esModels = getReviewerESProjects(projectIds, isClaim);
        // 将esModel转换成 WorkloadStatistics并过滤
        List<ClaimWorkloadStatistics> statistics =
            esModels.stream()
                .map(this::reviewerClaimMap)
                .filter(Objects::nonNull)
                .filter(isClaim ? this::isValidUnderwritingReviewer : this::isValidClaimReviewer)
                .collect(Collectors.toUnmodifiableList());
        return reduceReviewerStatistics(statistics);
    }

    private ClaimWorkloadStatistics reviewerClaimMap(ProjectEsModel model) {
        List<MemberInfo> memberInfos = model.getMembers();
        if (!CollectionUtils.isEmpty(memberInfos)) {

            MemberInfo info =
                memberInfos.stream()
                    .filter(
                        memberInfo -> UserAuthEnum.ROLE_REVIEWER.getAuth().equals(memberInfo.getAuth()))
                    .findFirst()
                    .orElse(null);
            if (info != null) {
                return new ClaimWorkloadStatistics(info.getId(), info.getName(), model.getProjectId());
            }
        }
        return null;
    }

    /** UW统计没有processor 和adjuster role的reviewer */
    private boolean isValidUnderwritingReviewer(ClaimWorkloadStatistics statistics) {
        return !isProcessor(statistics.getStaffId()) && !isAdjuster(statistics.getStaffId());
    }

    /** Claim统计没有processor role，但是同时有adjuster和reviewer role的reviewer */
    private boolean isValidClaimReviewer(ClaimWorkloadStatistics statistics) {
        return !isProcessor(statistics.getStaffId()) && isAdjuster(statistics.getStaffId());
    }

    /**
     * 根据员工将统计数据聚合
     *
     * @param statistics 每一条统计记录
     * @return 根据用户聚合的统计数据
     */
    private Collection<ClaimWorkloadStatistics> reduceReviewerStatistics(
        Collection<ClaimWorkloadStatistics> statistics) {
        return statistics.stream()
            .collect(
                Collectors.toMap(
                    ClaimWorkloadStatistics::getStaffId,
                    s -> s,
                    (s1, s2) ->
                        new ClaimWorkloadStatistics(s1.getStaffName(), s1.getCount() + s2.getCount())))
            .values()
            .stream()
            .sorted(Comparator.comparing(ClaimWorkloadStatistics::getStaffName))
            .collect(Collectors.toUnmodifiableList());
    }
}
