package com.bees360.service.firebase;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.firebase.FirebaseRoom;
import com.bees360.firebase.entity.FbCheckoutReason;
import com.bees360.firebase.entity.FirebaseCallRecord;
import com.bees360.firebase.entity.FirebaseFeedback;
import com.bees360.firebase.entity.FirebaseTimeline;
import com.bees360.job.registry.SerializableFirebaseBatch;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.google.cloud.firestore.CollectionReference;

import com.google.cloud.firestore.DocumentReference;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/8/25 3:41 下午
 */
public interface FirebaseService {

    String PROJECT_COLLECTION = "project";
    String MISSION_COLLECTION = "mission";
    String IBEES_MISSION_COLLECTION = "ibees_mission";
    String PILOT_COLLECTION = "pilot";
    String PILOT_BATCH_COLLECTION = "batch";
    String FEATURE_COLLECTION = "feature";
    String QUIZ = "quiz";
    String ATTACH_FILE = "attach_file";
    String HOVER_JOB = "hover_job";
    String MAGICPLAN = "magicplan";
    String ACTIVITY_RECORD = "activity_record";

    String getProjectDocumentPath(long projectId);

    void syncProjectToFirebaseWithFailedRetry(
            long projectId, String documentPath, Map<String, Object> fields, boolean updated);

    /**
     * 同步整个Project到Firebase
     * @param projectId project id
     */
    void syncProjectToFirebase(long projectId);

    /**
     * 将note更新到firebase的mission和project表里
     *
     * @param projectId 项目ID
     */
    void updateNoteToFirebase(long projectId);

    String getMissionDocumentKey(long projectId, String batchNo);

    /** 同步feedback到web */
    void syncFeedback(long projectId, long webPilotId, String pilotId, List<FirebaseFeedback> feedbacks);

    /**
     * 飞手接受batch后，将batch信息同步到web后台.
     *
     * @param batch
     */
    void syncBatchToWeb(SerializableFirebaseBatch batch, String batchId);

    /*
        临时方法begin
     */
    void syncCallRecord(List<FirebaseCallRecord> callRecord, long projectId, long webPilotId, String pilotId);

    void feedBackChanged(SerializableFirebaseMission mission, String missionId);

    void syncTimeline(long projectId, List<FirebaseTimeline> timelines, long pilotId);

    void syncQuizAnswers(long projectId, CollectionReference collection);

    void addCheckoutReason(long projectId, long pilotId, List<FbCheckoutReason> checkoutReasons);

    void syncAttachFiles(long projectId, String missionId);

    List<ProjectImage> getUploadedProjectImages(long projectId, long pilotId, CollectionReference image,Map<String, FirebaseRoom> roomMap);

    List<ProjectImage> getUploadedProjectImages(List<ProjectImage> images);

    long toWebUserId(String pilotId);

    /**
     * 获取Room信息
     *
     * @param missionPath mission 路径
     * @return Map
     */
    Map<String, FirebaseRoom> getRoomMap(String missionPath);

    /** 获取单个Room信息 */
    FirebaseRoom getRoom(DocumentReference roomReference);

    void deleteFirebaseImagesByOriginalFileNames(long projectId, List<String> originalFileNames);
}
