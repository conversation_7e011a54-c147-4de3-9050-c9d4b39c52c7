package com.bees360.scheduletask.project;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.event.SwyfftProjectAlertRegularly;
import com.bees360.service.MessageService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * using {@link SwyfftProjectAlertRegularly} instead.
 */
@Log4j2
@Deprecated
@Component
@ConditionalOnMissingBean(SwyfftProjectAlertRegularly.class)
public class SwyfftProjectAlertScheduler {

    @Autowired
    private MessageService messageService;

    private static final ZoneId zoneID = ZoneId.of(AmericaTimeZone.US_CENTRAL);

    @Scheduled(cron = "${app.web.scheduled.swyfft-project-alert.cron:0 0 9 * * MON}", zone = AmericaTimeZone.US_CENTRAL)
    void weeklySwyfftAlertStatistics() {
        ZonedDateTime now = ZonedDateTime.now(zoneID);
        LocalDate today = now.toLocalDate();
        LocalDate yesterday = today.minusDays(1);
        LocalDate lastMonday = today.minusDays(7);
        log.info("Send swyfft project alert with lastMonday {}", lastMonday);
        messageService.sendProjectAlerts(lastMonday.atStartOfDay(zoneID), yesterday.atTime(LocalTime.MAX).atZone(zoneID));
    }
}
