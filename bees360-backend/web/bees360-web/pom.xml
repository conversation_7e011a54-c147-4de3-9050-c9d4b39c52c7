<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360.web</groupId>
		<artifactId>bees360</artifactId>
		<version>${revision}${changelist}</version>
	</parent>
	<artifactId>bees360-web</artifactId>
    <packaging>jar</packaging>
	<name>bees360-web</name>

    <properties>
        <mainclass>com.bees360.web.Bees360WebApplication</mainclass>
    </properties>

	<dependencies>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-http-openapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-mail-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-flyzone-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-grpc-client</artifactId>
        </dependency>
      <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-job-api</artifactId>
      </dependency>
      <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-job-rabbit</artifactId>
      </dependency>
      <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-event-rabbit</artifactId>
      </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-map-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-grpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-http-auth</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-http-core</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-atomic-redis</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-project-grpc</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-project-http</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-activity-http</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-activity-grpc</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-activity-event</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-resource-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-beespilot-batch-grpc-client</artifactId>
            <version>${bees360-solid.version}</version>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-project-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-google</artifactId>
            <version>${bees360-solid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-monitor-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-integration-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-authing</artifactId>
        </dependency>
        <dependency>
            <groupId>co.realms9</groupId>
            <artifactId>realms9-bifrost-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>co.realms9</groupId>
            <artifactId>realms9-bifrost-grpc</artifactId>
        </dependency>
		<dependency>
			<groupId>com.bees360.web</groupId>
			<artifactId>bees360-web-grpc-service</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-manager-project</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-securityfirst</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-rct</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-openapi-grpc</artifactId>
            <version>${bees360-solid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-openapi-grpc-client</artifactId>
            <version>${bees360-solid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-openapi-http</artifactId>
            <version>${bees360-solid.version}</version>
        </dependency>
        <!-- Third Party -->

        <!-- ##### spring start #####  -->
        <dependency>
            <groupId>co.realms9</groupId>
            <artifactId>realms9-secretsmanger-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <!-- ##### spring end #####  -->

        <!-- database -->
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-image-grpc-client</artifactId>
        </dependency>
        <!-- apikey -->
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-apikey-grpc-client</artifactId>
        </dependency>
        <!-- email -->
        <dependency>
            <groupId>com.icegreen</groupId>
            <artifactId>greenmail</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.javafaker</groupId>
            <artifactId>javafaker</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-activity-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>logbook-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jmock</groupId>
            <artifactId>jmock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-imaging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api.grpc</groupId>
            <artifactId>proto-google-cloud-firestore-v1</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.auth</groupId>
            <artifactId>google-auth-library-oauth2-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.auth</groupId>
            <artifactId>google-auth-library-credentials</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-job-autoconfig</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <configuration>
                    <container>
                        <mainClass>${mainclass}</mainClass>
                    </container>
                    <extraDirectories>
                        <permissions>
                            <permission>
                                <file>/var/bees360/www/logs</file>
                                <mode>755</mode>
                            </permission>
                        </permissions>
                    </extraDirectories>
                    <from>
                        <image>harbor.9realms.co/private/upstream-base:git-9d5b5a2b</image>
                    </from>
                </configuration>
                <executions>
                    <execution>
                        <id>upstream-base</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <from>
                                <image>harbor.9realms.co/private/upstream-base:git-9d5b5a2b</image>
                            </from>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <mainClass>${mainclass}</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
