package com.bees360.web.openapi;

import java.net.URI;
import java.net.URL;

import jakarta.servlet.http.HttpServletResponse;

import com.bees360.resource.HttpResourceClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.entity.ProjectImage;
import com.bees360.service.ProjectImageService;
import com.bees360.util.httputil.UrlRedirection;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020-08-22
 */
@RestController
@RequestMapping("/v1/image")
@Slf4j
public class OpenApiImageController {

    @Autowired
    private ProjectImageService projectImageService;

    @Autowired
    private UrlRedirection urlRedirection;

    @Autowired
    private HttpResourceClient httpResourceClient;

    /**
     * 下载图片(获取图片下载的重定向url)
     *
     * @param imageId
     * @param httpServletResponse
     * @throws Exception
     */
    @GetMapping("/{imageId}/file")
    private void redirectImage(@PathVariable String imageId, HttpServletResponse httpServletResponse)
        throws Exception {
        ProjectImage projectImage = projectImageService.getImageById(imageId);
        if (projectImage == null || StringUtils.isBlank(projectImage.getFileName())) {
            throw new ResourceNotFoundException("image " + imageId + " not found.");
        }
        URL resourceUrl = httpResourceClient.asResourceUrlProvider().getGetUrl(projectImage.getFileName());
        urlRedirection.redirect(httpServletResponse, HttpStatus.TEMPORARY_REDIRECT, "image/jpeg", resourceUrl.toString());
    }
}
