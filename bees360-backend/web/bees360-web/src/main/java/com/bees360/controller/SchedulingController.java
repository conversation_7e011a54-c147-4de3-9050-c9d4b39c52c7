package com.bees360.controller;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.scheduletask.project.ScheduledCompanyProjectStatistics;
import com.bees360.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2019/07/23
 */
@RestController
@RequestMapping("/scheduling")
public class SchedulingController {

    @Autowired private ScheduledCompanyProjectStatistics scheduledCompanyProjectStatistics;

    @Autowired
    private MessageService messageService;

    @PostMapping("/proStatPerCom")
    public void doProjectStatSending() {
        scheduledCompanyProjectStatistics.sendCompanyProjectStats();
    }

    @PostMapping("/swyfft_alert")
    public void intervalAlertEmail(String startDate, String endDate) {
        ZoneId zoneID = ZoneId.of(AmericaTimeZone.US_CENTRAL);
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_DATE);
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ISO_DATE);
        messageService.sendProjectAlerts(start.atStartOfDay(zoneID), end.atTime(LocalTime.MAX).atZone(zoneID));
    }
}
