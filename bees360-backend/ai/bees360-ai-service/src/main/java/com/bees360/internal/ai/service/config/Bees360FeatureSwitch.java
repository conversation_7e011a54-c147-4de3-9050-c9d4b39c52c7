package com.bees360.internal.ai.service.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "bees360.feature-switch")
public class Bees360FeatureSwitch {
    private boolean enableOpenCloseRead;
    private boolean enableUserKey;
    private boolean enableFillTimeZoneWhenGetProject;
    private boolean enablePartiallySyncToEs = false;
    private boolean enableStagingReportSummaryByRedis = false;
    private boolean enableInvoiceStatus = false;
    private boolean enableNoCommasPadding = false;
    private boolean enablePrintingAiEsCountTime = true;
    private boolean enableSyncFirebaseHoverStatus = true;
    private boolean enableSyncAiEsDataToSolid = false;
    private boolean enableCheckCloseProjectTargetStatus = false;
    private boolean enableNewGetOverviewLogic = false;
    private boolean enableDeleteEdgeAnnotation = false;
    private boolean needCheckUploadExteriorImagesTaskStatus = true;
    private boolean needCheckUnfinishedJobBeforeProcessingReport = false;
    private boolean enableAddNumberTagFromImageCategory = true;
    private boolean enableAddNumberTagWithNewLogic = false;
    private boolean enableAutoTagWithNewLogic = false;
    private boolean enableDeleteMysqlFullAnnotation = true;
    private boolean enableAutoDeleteDarTag = true;
    private boolean enableCloseClaimEmail = false;
    private boolean enableSkipSetMemberOnSystemSetTaskOwner = true;
}
