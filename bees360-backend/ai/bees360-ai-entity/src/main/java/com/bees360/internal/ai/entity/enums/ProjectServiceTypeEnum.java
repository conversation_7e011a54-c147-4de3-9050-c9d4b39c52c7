package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.project.ProjectTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/03/19
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ProjectServiceTypeEnum implements BaseCodeEnum {
    /** */
    QUICK_INSPECT(
            0,
            "Limited Inspection",
            "Limited Inspection",
            List.of(
                    ReportTypeEnum.PROPERTY_IMAGE_REPORT,
                    ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT),
            List.of(
                    ReportTypeEnum.INVOICE,
                    ReportTypeEnum.DRONE_PHOTO_SHEET,
                    ReportTypeEnum.MOBILE_PHOTO_SHEET,
                    ReportTypeEnum.GENERAL_LOSS_REPORT),
            ProjectTypeEnum.CLAIM),
    /** */
    FULL_ADJUSTMENT(
            1,
            "Full Inspection",
            "Full Inspection",
            List.of(
                    ReportTypeEnum.PROPERTY_IMAGE_REPORT,
                    ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT),
            List.of(
                    ReportTypeEnum.INVOICE,
                    ReportTypeEnum.ESTIMATE_REPORT,
                    ReportTypeEnum.DRONE_PHOTO_SHEET,
                    ReportTypeEnum.MOBILE_PHOTO_SHEET,
                    ReportTypeEnum.GENERAL_LOSS_REPORT),
            ProjectTypeEnum.CLAIM),
    /** */
    ROOF_ONLY_UNDERWRITING(
            2,
            "Roof Only",
            "Roof-only Underwriting",
            ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT,
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),
    /** */
    EXTERIOR_UNDERWRITING(
            3,
            "Exterior",
            "Exterior Underwriting",
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),
    /** */
    FOUR_POINT_UNDERWRITING(
            4,
            "4-Point",
            "4-Point Underwriting",
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),
    /** */
    FOUR_POINT_SELF_UNDERWRITING(
            5,
            "4-Point Self",
            "4-Point Self Underwriting",
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),

    /** 当理赔完成，房屋修理完成之后，重新对房屋屋顶进行检测，确保房屋的屋顶按照理赔的结果进行修缮，以防屋主骗保。 */
    POST_CONSTRUCTION_AUDIT(
            6,
            "Post-Construction Audit",
            "Post-Construction Audit",
            ReportTypeEnum.POST_CONSTRUCTION_AUDIT_REPORT,
            List.of(ReportTypeEnum.INVOICE),
            ProjectTypeEnum.CLAIM),

    PREMIUM_FOUR_POINT_UNDERWRITING(
            7,
            "Premium 4-Point Underwriting",
            "Premium 4-Point Underwriting",
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),

    EXPRESS_UNDERWRITING(
            8,
            "Express Underwriting",
            "Express Underwriting",
            ReportTypeEnum.EUR,
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),

    WHITE_GLOVE(
            9,
            "High Value Premium 4-point",
            "High Value Premium 4-point",
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),

    EXPRESS_INSPECTION(
            10,
            "Express Inspection",
            "Express Inspection",
            List.of(
                    ReportTypeEnum.PROPERTY_IMAGE_REPORT,
                    ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT),
            List.of(
                    ReportTypeEnum.INVOICE,
                    ReportTypeEnum.ESTIMATE_REPORT,
                    ReportTypeEnum.DRONE_PHOTO_SHEET,
                    ReportTypeEnum.MOBILE_PHOTO_SHEET,
                    ReportTypeEnum.GENERAL_LOSS_REPORT),
            ProjectTypeEnum.CLAIM),

    SCHEDULING_ONLY(
            11,
            "Scheduling Only",
            "Scheduling Only",
            List.of(ReportTypeEnum.SCHEDULING_ONLY_SUMMARY),
            List.of(ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),

    COMMERCIAL_UNDERWRITING(
            12,
            "Commercial Underwriting",
            "Commercial Underwriting",
            List.of(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT),
            List.of(ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
            ProjectTypeEnum.UNDERWRITING),

    LIMITED_EXTERIOR_UNDERWRITING(
        13,
        "Limited Exterior",
        "Limited Exterior Underwriting",
        List.of(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT),
        List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT, ReportTypeEnum.SNAP),
        ProjectTypeEnum.UNDERWRITING),

    HIGH_LEVEL_OVERVIEW(
            14,
            "High Level Overview",
            "High Level Overview",
            List.of(ReportTypeEnum.SNAP),
            List.of(ReportTypeEnum.INVOICE, ReportTypeEnum.CUSTOM_HOME_REPORT, ReportTypeEnum.ESTIMATE_REPORT),
            ProjectTypeEnum.UNDERWRITING),

    PHOTO_ONLY_INSPECTION(
            15,
            "Photo Only Inspection",
            "Photo Only Inspection",
            List.of(ReportTypeEnum.DRONE_PHOTO_SHEET, ReportTypeEnum.MOBILE_PHOTO_SHEET),
            List.of(
                    ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
                    ReportTypeEnum.INVOICE,
                    ReportTypeEnum.DRONE_PHOTO_SHEET,
                    ReportTypeEnum.MOBILE_PHOTO_SHEET),
            ProjectTypeEnum.UNDERWRITING);

    private final int code;
    private final String value;
    private final String display;
    private final List<ReportTypeEnum> reportTypes;

    /** 在editor中需要额外增加的报告类型 */
    private final List<ReportTypeEnum> editorExpandReports;

    private final ProjectTypeEnum projectType;

    ProjectServiceTypeEnum(
            int code,
            String value,
            String display,
            ReportTypeEnum reportTypeEnum,
            List<ReportTypeEnum> editorExpandReports,
            ProjectTypeEnum projectType) {
        this(code, value, display, List.of(reportTypeEnum), editorExpandReports, projectType);
    }

    ProjectServiceTypeEnum(
            int code,
            String value,
            String display,
            ReportTypeEnum reportTypeEnum,
            ProjectTypeEnum projectType) {
        this(code, value, display, List.of(reportTypeEnum), List.of(), projectType);
    }

    ProjectServiceTypeEnum(
            int code,
            String value,
            String display,
            List<ReportTypeEnum> reportTypes,
            List<ReportTypeEnum> editorExpandReports,
            ProjectTypeEnum projectType) {
        this.code = code;
        this.value = value;
        this.display = display;
        this.reportTypes = reportTypes;
        this.editorExpandReports = editorExpandReports;
        this.projectType = projectType;
    }

    public static ProjectServiceTypeEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(ProjectServiceTypeEnum.values())
                .filter(t -> Objects.equals(t.getCode(), code))
                .findFirst()
                .orElse(null);
    }

    public static boolean isClaim(int serviceType) {
        return getClaimCodes().contains(serviceType);
    }

    public static List<Integer> getClaimCodes() {
        return Arrays.stream(values())
                .filter(t -> Objects.equals(t.getProjectType(), ProjectTypeEnum.CLAIM))
                .map(ProjectServiceTypeEnum::getCode)
                .collect(Collectors.toList());
    }

    public static boolean isUnderWriting(int serviceType) {
        return getUnderWritingCodes().contains(serviceType);
    }

    public static List<Integer> getUnderWritingCodes() {
        return Arrays.stream(values())
            .filter(t -> Objects.equals(t.getProjectType(), ProjectTypeEnum.UNDERWRITING))
            .map(ProjectServiceTypeEnum::getCode)
            .collect(Collectors.toList());
    }

    public static boolean exist(Integer code) {
        return code != null && getEnum(code) != null;
    }

    public static ProjectServiceTypeEnum getEnumByValue(String value) {
        // todo : temporary for white glove service name change, should be removed after complete change
        if (Objects.equals("White Glove Service", value)) {
            return WHITE_GLOVE;
        }
        for (ProjectServiceTypeEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    public static List<CodeNameDto> getDict() {
        return Arrays.stream(ProjectServiceTypeEnum.values())
                .map(o -> new CodeNameDto(o.getCode(), o.getDisplay()))
                .collect(Collectors.toList());
    }

    public static boolean exist(String value) {
        return getEnumByValue(value) != null;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    @Override
    @JsonValue
    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public List<ReportTypeEnum> getReportTypes() {
        return reportTypes;
    }

    public List<ReportTypeEnum> getEditorExpandReports() {
        return editorExpandReports;
    }

    public ProjectTypeEnum getProjectType() {
        return projectType;
    }

    public boolean containsReport(int reportType) {
        return reportTypes.stream().anyMatch(t -> Objects.equals(t.getCode(), reportType));
    }
}
