package com.bees360.web.openapi;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSON;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.address.AddressHiveLocationProvider;
import com.bees360.address.AddressManager;
import com.bees360.address.Message;
import com.bees360.beespilot.batch.BeesPilotBatchProvider;
import com.bees360.contact.ContactRecordManager;
import com.bees360.contract.ContractManager;
import com.bees360.customer.CustomerPolicyTypeManager;
import com.bees360.customer.DivisionManager;
import com.bees360.entity.Company;
import com.bees360.entity.CompanyIDMap;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.openapi.OpenProjectCreateVo;
import com.bees360.entity.openapi.OpenProjectVo;
import com.bees360.event.EventPublisher;
import com.bees360.flyzone.FlyZoneTypeProvider;
import com.bees360.mapper.AddressAirspaceMapper;
import com.bees360.mapper.BeesPilotStatusMapper;
import com.bees360.mapper.CompanyMapper;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectAirspaceMapper;
import com.bees360.mapper.ProjectCustomizedInfoMapper;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectInspectionScheduleMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectOperatingCompanyMapper;
import com.bees360.mapper.ProjectStateMapper;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.ProjectPipelineConfigService;
import com.bees360.policy.Coverage;
import com.bees360.policy.Policy;
import com.bees360.policy.PolicyManager;
import com.bees360.project.BuildingManager;
import com.bees360.project.ContactManager;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectDaysOldProvider;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectPaymentManager;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.SimilarProjectProvider;
import com.bees360.project.claim.ProjectCatastropheManager;
import com.bees360.project.member.MemberManager;
import com.bees360.project.report.ProjectReportJobManager;
import com.bees360.project.state.ChangeReasonFromCategoryProvider;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateProvider;
import com.bees360.project.status.ProjectStatusProvider;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.service.ActivityService;
import com.bees360.service.CommentService;
import com.bees360.service.CompanyService;
import com.bees360.service.ConstantSettingService;
import com.bees360.service.ContextProvider;
import com.bees360.service.EventHistoryService;
import com.bees360.service.MemberService;
import com.bees360.service.MessageService;
import com.bees360.service.NotificationService;
import com.bees360.service.ProjectInspectionService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectReportService;
import com.bees360.service.ProjectScoreService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.ProjectTaskService;
import com.bees360.service.SystemConfigService;
import com.bees360.service.UserService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.impl.ProjectServiceImpl;
import com.bees360.service.member.PostgresMemberManagerAdapter;
import com.bees360.service.openapi.ProjectCreationPrehandle;
import com.bees360.service.openapi.impl.OpenApiProjectServiceImpl;
import com.bees360.service.projectII.ProjectIIServiceAdapter;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.service.threadpool.AsyncCreateBeeObjectThreadPool;
import com.bees360.service.util.ProjectSupplementalServiceTypeUtil;
import com.bees360.user.BifrostUserMapperAdapter;
import com.bees360.user.UserProvider;
import com.bees360.util.httputil.UrlRedirection;
import com.bees360.util.project.ProjectImageArchiveKeyConverter;
import com.bees360.web.core.properties.bean.SystemConfig;
import com.google.cloud.firestore.Firestore;
import com.google.common.base.Functions;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@WebMvcTest(OpenApiProjectController.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-OpenReportSummaryEndpointTest.yaml")
@Import({
    OpenApiProjectControllerNewTest.Config.class,
})
@AutoConfigureMockMvc(addFilters = false)
public class OpenApiProjectControllerNewTest {

    @MockBean private ProjectImageArchiveKeyConverter projectImageArchiveKeyConverter;
    @MockBean private UrlRedirection urlRedirection;
    @MockBean private SystemConfig systemConfig;
    @MockBean private ResourceUrlProvider resourceUrlProvider;
    @MockBean private ProjectStatusService projectStatusService;
    @MockBean private ProjectReportFileService projectReportFileService;
    @MockBean private ProjectCreationPrehandle projectCreationPrehandle;
    @MockBean private CompanyService companyService;
    @MockBean private ProjectSupplementalServiceTypeUtil projectSupplementalServiceTypeUtil;
    @MockBean private DivisionManager divisionManager;
    @MockBean private Bees360CompanyConfig companyConfig;
    @MockBean private ProjectMapper projectMapper;
    @MockBean private ProjectMessageService projectMessageService;
    @MockBean private UserService userService;
    @MockBean private CompanyMapper companyMapper;
    @MockBean private MessageService messageService;
    @MockBean private MemberMapper memberMapper;
    @MockBean private EventHistoryService eventHistoryService;
    @MockBean private NotificationService notificationService;
    @MockBean private ProjectImageMapper projectImageMapper;
    @MockBean private ProjectReportService projectReportService;
    @MockBean private ProjectTaskService projectTaskService;
    @MockBean private ProjectCustomizedInfoMapper projectCustomizedInfoMapper;
    @MockBean private SystemConfigService systemConfigService;
    @MockBean private FirebaseService firebaseService;
    @MockBean private ContextProvider springSecurityContextProvider;
    @MockBean private BeesPilotStatusService beesPilotStatusService;
    @MockBean private BeesPilotStatusMapper beesPilotStatusMapper;
    @MockBean private MemberService memberService;
    @MockBean private BeesPilotBatchItemService beesPilotBatchItemService;
    @MockBean private ProjectScoreService projectScoreService;
    @MockBean private BeesPilotBatchService beesPilotBatchService;
    @MockBean private ProjectLabelService projectLabelService;
    @MockBean private ConstantSettingService constantSettingService;
    @MockBean private ProjectInspectionService projectInspectionService;
    @MockBean private ActivityService activityService;
    @MockBean private CommentService commentService;
    @MockBean private CompanyIDMap companyIDMap;
    @MockBean private AsyncCreateBeeObjectThreadPool asyncCreateBeeObjectThreadPool;
    @MockBean private MessageSource messageSource;
    @MockBean private FlyZoneTypeProvider flyZoneTypeProvider;
    @MockBean private ResourcePool resourcePool;
    @MockBean private EventPublisher eventPublisher;
    @MockBean private UserProvider userProvider;
    @MockBean private ProjectTagManager projectTagManager;
    @MockBean private AddressManager addressManager;
    @MockBean private ProjectIIManager projectIIManager;
    @MockBean private ProjectOperatingCompanyMapper operatingCompanyMapper;
    @MockBean private Firestore firestore;
    @MockBean private AddressHiveLocationProvider addressHiveLocationProvider;
    @MockBean private PipelineService pipelineService;
    @MockBean private ProjectCatastropheManager projectCatastropheManager;
    @MockBean private CommentManager commentManager;
    @MockBean private ContactManager contactManager;
    @MockBean private PolicyManager policyManager;
    @MockBean private PostgresMemberManagerAdapter postgresMemberManagerAdapter;
    @MockBean private ProjectInspectionScheduleMapper projectInspectionScheduleMapper;
    @MockBean private ContactRecordManager contactRecordManager;
    @MockBean private BeesPilotBatchProvider beesPilotBatchProvider;
    @MockBean private ProjectDaysOldProvider daysOldProvider;
    @MockBean private ProjectStateChangeReasonManager stateChangeReasonManager;
    @MockBean private Bees360FeatureSwitch bees360FeatureSwitch;
    @MockBean private ProjectStateProvider projectStateProvider;
    @MockBean private ProjectPaymentManager projectPaymentManager;
    @MockBean private MemberManager memberManager;
    @MockBean private SimilarProjectProvider similarProjectProvider;
    @MockBean private ActivityManager activityManager;
    @MockBean private AddressAirspaceMapper addressAirspaceMapper;
    @MockBean private ProjectAirspaceMapper projectAirspaceMapper;
    @MockBean private ExternalIntegrationManager externalIntegrationManager;
    @MockBean private ProjectStateMapper projectStateMapper;
    @MockBean private ChangeReasonFromCategoryProvider changeReasonFromCategoryProvider;
    @MockBean private Function<Message.AddressQueryRequest, ProjectTypeEnum> getPropertyByHazardHub;
    @MockBean private Function<com.bees360.address.Message.AddressQueryRequest, Integer> getLivingAreaByHazardHub;
    @MockBean private Function<String, URI> convertAvatarToUri;
    @MockBean(name = "closeoutGeneratePredicate") private Predicate<Project> closeoutGeneratePredicate;
    @MockBean private ProjectStatusProvider projectStatusProvider;
    @MockBean(name = "CompanyAdminAssignableMemberRole") private List<RoleEnum> companyAdminAssignableRoles;
    @MockBean private BifrostUserMapperAdapter bifrostUserMapperAdapter;
    @MockitoBean CustomerPolicyTypeManager customerPolicyTypeManager;
    @MockitoBean ContractManager contractManager;
    @MockitoBean BuildingManager buildingManager;
    @MockitoBean GenericProjectCreator genericProjectCreator;
    @MockitoBean ProjectPipelineConfigService projectPipelineConfigService;
    @MockitoBean ApplicationEventPublisher applicationEventPublisher;
    @MockitoBean ProjectPolicyManager projectPolicyManager;

    @MockitoBean ProjectReportJobManager projectReportJobManager;

    @Autowired
    private MockMvc mockMvc;



    @MockitoSpyBean private ProjectIIServiceAdapter projectIIServiceAdapter;

    public OpenApiProjectControllerNewTest() {
    }

    @Import({
        OpenApiProjectServiceImpl.class,
        OpenApiProjectController.class,
        ProjectIIServiceAdapter.class,
        Bees360FeatureSwitch.class,
    })
    @Configuration
    static class Config {
        @Bean("projectService")
        public ProjectService projectService() {
            var projectClientReceived = new Project();
            projectClientReceived.setProjectId(1213);
            projectClientReceived.setProjectStatus(NewProjectStatusEnum.CLIENT_RECEIVED.getCode());
            var projectReturnedToClient = new Project();
            projectReturnedToClient.setProjectId(1212);
            projectReturnedToClient.setProjectStatus(NewProjectStatusEnum.RETURNED_TO_CLIENT.getCode());

            var projects = Stream.of(projectClientReceived, projectReturnedToClient).collect(Collectors.toMap(Project::getProjectId, Functions.identity()));

            ProjectService spy = Mockito.spy(ProjectServiceImpl.class);
            doAnswer(arg -> {
                long projectId = arg.getArgument(0);
                return projects.getOrDefault(projectId, new Project());
            }).when(spy).getById(anyLong());
            return spy;
        }
    }

    @Test
    void testCreateProjectWithCoverage() throws Exception {
        String url = "/v1/project?userId=10001";
        String requestJson = loadResource("openapi/test_openapi_create_project_with_coverage.json");
        OpenProjectCreateVo openProjectCreateVo = JSON.parseObject(requestJson, OpenProjectCreateVo.class);
        Double dwellingCoverage = openProjectCreateVo.getDwellingCoverage();
        Double otherStructureCoverage = openProjectCreateVo.getOtherStructureCoverage();
        Double contentCoverage = openProjectCreateVo.getContentCoverage();
        Double livingArea = openProjectCreateVo.getCarrierProvidedLivingArea();

        mockSomeBean();
        var policy = getPolicy(dwellingCoverage, otherStructureCoverage, contentCoverage);
        when(projectIIManager.findById(anyString()))
            .thenReturn(ProjectII.projectBuilder
                .newBuilder()
                .setPolicy(policy)
                .build());

        MockHttpServletResponse response = sendPostRequest(url, requestJson);

        ArgumentCaptor<Project> captor = ArgumentCaptor.forClass(Project.class);
        Mockito.verify(projectIIServiceAdapter)
            .createProjectNew(anyLong(),
                captor.capture(),
                Mockito.anyString(),
                any(),
                anyBoolean());
        Assertions.assertEquals(dwellingCoverage, captor.getValue().getDwellingCoverage());
        Assertions.assertEquals(otherStructureCoverage, captor.getValue().getOtherStructureCoverage());
        Assertions.assertEquals(contentCoverage, captor.getValue().getContentCoverage());
        Assertions.assertEquals(livingArea, captor.getValue().getCarrierProvidedLivingArea());

        assertCoverage(response, dwellingCoverage, otherStructureCoverage, contentCoverage);
        assertPolicy(response , policy);
    }

    @Test
    void testCreateProjectWithNoCoverage() throws Exception {
        String url = "/v1/project?userId=10001";
        String requestJson = loadResource("openapi/test_openapi_create_project_with_no_coverage.json");
        OpenProjectCreateVo openProjectCreateVo = JSON.parseObject(requestJson, OpenProjectCreateVo.class);
        Double dwellingCoverage = openProjectCreateVo.getDwellingCoverage();
        Double otherStructureCoverage = openProjectCreateVo.getOtherStructureCoverage();
        Double contentCoverage = openProjectCreateVo.getContentCoverage();

        mockSomeBean();
        var policy = Policy.from(com.bees360.policy.Message.PolicyMessage.newBuilder().setType("Other").build());
        when(projectIIManager.findById(anyString()))
            .thenReturn(ProjectII.projectBuilder
                .newBuilder()
                .setPolicy(policy)
                .build());

        MockHttpServletResponse response = sendPostRequest(url, requestJson);

        assertCoverage(response, dwellingCoverage, otherStructureCoverage, contentCoverage);
        assertPolicy(response, policy);
    }

    @Test
    void testGetProjectForCoverage() throws Exception {
        Double dwellingCoverage = 1.5D;
        Double otherStructureCoverage = 2.5D;
        Double contentCoverage = 3.5D;
        String url = "/v1/project/11111?userId=10001";

        when(projectIIManager.findById(anyString()))
            .thenReturn(ProjectII.projectBuilder
                .newBuilder()
                .setPolicy(getPolicy(dwellingCoverage, otherStructureCoverage, contentCoverage))
                .build());

        MockHttpServletResponse response = sendGetRequest(url);

        assertCoverage(response, dwellingCoverage, otherStructureCoverage, contentCoverage);
    }

    @Test
    void testGetProjectForNoCoverage() throws Exception {
        Double dwellingCoverage = null;
        Double otherStructureCoverage = null;
        Double contentCoverage = null;
        String url = "/v1/project/11111?userId=10001";

        when(projectIIManager.findById(anyString()))
            .thenReturn(ProjectII.projectBuilder
                .newBuilder()
                .setPolicy(Policy.from(com.bees360.policy.Message.PolicyMessage.newBuilder().setType("Other").build()))
                .build());
        MockHttpServletResponse response = sendGetRequest(url);

        assertCoverage(response, dwellingCoverage, otherStructureCoverage, contentCoverage);
    }

    @Test
    void testUpdateProjectStatus() throws Exception {
        var url = "/v1/project/1212/status?userId=10012";
        var requestJson = "{\"value\":\"Client Received\"}";

        when(projectStatusService.changeOnClientReceived(anyLong(), anyLong(), anyString())).thenReturn(new ProjectStatus());

        var response = sendPutRequest(url, requestJson);
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatus());

        var content = response.getContentAsString();
        JSONAssert.assertEquals(
        "{\"project\": [{\"id\":1212,\"status\":\"Client Received\"}]}", content, true);
    }

    @Test
    void testUpdateProjectStatusWithSameStatus() throws Exception {
        var url = "/v1/project/1213/status?userId=10012";
        var requestJson = "{\"value\":\"Client Received\"}";

        when(projectStatusService.changeOnClientReceived(anyLong(), anyLong(), anyString())).thenReturn(new ProjectStatus());

        var response = sendPutRequest(url, requestJson);
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatus());

        var content = response.getContentAsString();
        JSONAssert.assertEquals(
            "{\"project\": [{\"id\":1213,\"status\":\"Client Received\"}]}", content, true);
    }

    private Policy getPolicy(Double dwellingCoverage, Double otherStructureCoverage, Double contentCoverage) {
        List<Coverage> coverages = new ArrayList<>();
        var dwelling = Coverage.from(
            com.bees360.policy.Message.PolicyMessage.CoverageMessage.newBuilder()
                .setType(com.bees360.policy.Message.CoverageType.DWELLING.name())
                .setAmount(dwellingCoverage)
                .build());
        coverages.add(dwelling);
        var otherStructure = Coverage.from(
            com.bees360.policy.Message.PolicyMessage.CoverageMessage.newBuilder()
                .setType(com.bees360.policy.Message.CoverageType.OTHER_STRUCTURE.name())
                .setAmount(otherStructureCoverage)
                .build());
        coverages.add(otherStructure);
        var content = Coverage.from(
            com.bees360.policy.Message.PolicyMessage.CoverageMessage.newBuilder()
                .setType(com.bees360.policy.Message.CoverageType.CONTENT.name())
                .setAmount(contentCoverage)
                .build());
        coverages.add(content);
        return Policy.PolicyBuilder.newBuilder().setType("Other").setCoverage(coverages).build();
    }

    private void mockSomeBean() {
        when(projectSupplementalServiceTypeUtil.checkValid(any()))
            .thenReturn(true);
        when(projectCreationPrehandle.handle(anyLong(), any()))
            .thenReturn(null);
        when(companyService.getIdByKey("Swyfft Underwriting"))
            .thenReturn(2357L);
        Company company = new Company();
        company.setCompanyId(1062);
        when(companyService.getByUserId(anyLong()))
            .thenReturn(company);
        Bees360CompanyConfig.ContractConfig contract = new Bees360CompanyConfig.ContractConfig();
        contract.setProcessedBy("1062");
        Bees360CompanyConfig.CompanyConfigItem companyDataset = new Bees360CompanyConfig.CompanyConfigItem();
        companyDataset.setContract(contract);
        when(companyConfig.findConfig(anyLong()))
            .thenReturn(companyDataset);
        var project = new Project();
        project.setProjectId(1213);
        when(genericProjectCreator.create(
                        any(ProjectCreationRequest.class), anyBoolean(), anyString()))
                .thenAnswer(
                        args -> {
                            var request = args.getArgument(0, ProjectCreationRequest.class);
                            Assertions.assertEquals(
                                    com.bees360.building.Message.BuildingType.UNKNOWN_BUILDING_TYPE,
                                    request.getPolicy().getBuilding().getType());
                            return ProjectII.from(
                                    request.toMessage().toBuilder().setId("1213").build());
                        });
        when(projectMapper.getById(anyLong())).thenReturn(project);
    }

    private void assertCoverage(MockHttpServletResponse response,
                                Double dwellingCoverage,
                                Double otherStructureCoverage,
                                Double contentCoverage) throws UnsupportedEncodingException {
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatus());
        String contentAsString = response.getContentAsString();
        TypeToken typeToken = new TypeToken<OpenProjectVo.OpenProjectVoListWrapper<OpenProjectVo>>(){};
        OpenProjectVo.OpenProjectVoListWrapper<OpenProjectVo> object = new Gson().fromJson(contentAsString, typeToken.getType());
        List<OpenProjectVo> projects = object.getProject();
        OpenProjectVo openProjectVo = projects.get(0);
        Assertions.assertEquals(dwellingCoverage, openProjectVo.getDwellingCoverage());
        Assertions.assertEquals(otherStructureCoverage, openProjectVo.getOtherStructureCoverage());
        Assertions.assertEquals(contentCoverage, openProjectVo.getContentCoverage());
    }

    private void assertPolicy(MockHttpServletResponse response, Policy policy) throws UnsupportedEncodingException {
        String contentAsString = response.getContentAsString();
        TypeToken typeToken = new TypeToken<OpenProjectVo.OpenProjectVoListWrapper<OpenProjectVo>>(){};
        OpenProjectVo.OpenProjectVoListWrapper<OpenProjectVo> object = new Gson().fromJson(contentAsString, typeToken.getType());
        List<OpenProjectVo> projects = object.getProject();
        OpenProjectVo openProjectVo = projects.get(0);
        Assertions.assertEquals(policy.getType(), openProjectVo.getPolicyType());
    }

    private String loadResource(String name) throws IOException {
        return IOUtils.resourceToString(name, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }

    private MockHttpServletResponse sendPutRequest(String url, String requestJson) throws Exception {
        MockHttpServletResponse response = mockMvc.perform(MockMvcRequestBuilders.put(url)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
            .andReturn()
            .getResponse();
        return response;
    }

    private MockHttpServletResponse sendPostRequest(String url, String requestJson) throws Exception {
        MockHttpServletResponse response = mockMvc.perform(MockMvcRequestBuilders.post(url)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
            .andReturn()
            .getResponse();
        return response;
    }

    private MockHttpServletResponse sendGetRequest(String url) throws Exception {
        MockHttpServletResponse response = mockMvc.perform(MockMvcRequestBuilders.get(url))
            .andReturn()
            .getResponse();
        return response;
    }
}
