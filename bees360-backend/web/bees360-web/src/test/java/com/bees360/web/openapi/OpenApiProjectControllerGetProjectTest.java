package com.bees360.web.openapi;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.config.WebMvcConfig;
import com.bees360.config.support.ControllerExceptionHandler;
import com.bees360.config.support.CurUserIdMethodArgumentResolver;
import com.bees360.entity.Project;
import com.bees360.entity.openapi.OpenProjectVo;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.policy.Message;
import com.bees360.policy.Policy;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.service.ContextProvider;
import com.bees360.service.MessageService;
import com.bees360.service.ProjectService;
import com.bees360.service.openapi.OpenApiProjectService;
import com.bees360.service.openapi.converter.ProjectConverter;
import com.bees360.service.properties.Bees360WatcherProperties;
import com.bees360.util.httputil.UrlRedirection;
import com.bees360.util.project.ProjectImageArchiveKeyConverter;
import com.bees360.web.core.properties.bean.SystemConfig;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.quartz.QuartzProperties;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@Import({
    OpenApiProjectControllerGetProjectTest.Config.class,
    OpenApiProjectController.class,
})
@WebMvcTest(OpenApiProjectController.class)
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.location = classpath:application-OpenApiProjectControllerGetProjectTest.yaml")
class OpenApiProjectControllerGetProjectTest {

    @Import({
        CurUserIdMethodArgumentResolver.class,
        WebMvcConfig.class,
        ControllerExceptionHandler.class,
    })
    @Configuration
    static class Config {

        @Bean
        ContextProvider contextProvider() {
            return new ContextProvider() {
                @Override
                public long getUserIdFromContext() {
                    return 10001L;
                }
            };
        }

        @Bean
        MessageService messageService() {
            return Mockito.mock(MessageService.class);
        }

        @Bean
        ApiExceptionHandler apiExceptionHandler() {
            return new ApiExceptionHandler();
        }
    }

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProjectService projectService;

    @MockBean
    private OpenApiProjectService openApiProjectService;

    @MockBean
    private ProjectImageArchiveKeyConverter projectImageArchiveKeyConverter;

    @MockBean
    private UrlRedirection urlRedirection;

    @MockBean
    private SystemConfig systemConfig;

    @MockBean
    private ResourceUrlProvider resourceUrlProvider;

    @MockBean
    private QuartzProperties quartzProperties;

    @MockBean
    private Bees360WatcherProperties bees360WatcherProperties;

    @Test
    void testConvertProjectToVo() throws Exception {
        // Arrange
        long projectId = 11111L;

        Project project = new Project();
        project.setProjectId(projectId);
        project.setAddress("11 Howard St");
        project.setCity("New York");
        project.setState("NY");
        project.setLat(37.4222804);
        project.setLng(-122.0843428);
        var policy = Policy.from(Message.PolicyMessage.newBuilder().setType("test").build());
        var projectVo = ProjectConverter.toOpenProjectVo(project, "Insured By", policy);
        assertEquals(projectId, projectVo.getId());
        assertEquals("Insured By", projectVo.getInsuredBy());
        assertEquals("11 Howard St", projectVo.getStreetAddress());
        assertEquals("New York", projectVo.getCity());
        assertEquals("NY", projectVo.getState());
        assertEquals(37.4222804, projectVo.getLat());
        assertEquals(-122.0843428, projectVo.getLng());
    }

    @Test
    void testGetProject_ShouldReturnProject_WhenValidRequest() throws Exception {
        // Arrange
        long userId = 10001L;
        long projectId = 11111L;

        OpenProjectVo mockProjectVo = new OpenProjectVo();
        mockProjectVo.setId(projectId);
        mockProjectVo.setStreetAddress("11 Howard St");
        mockProjectVo.setCity("New York");
        mockProjectVo.setState("NY");
        mockProjectVo.setLat(37.4222804);
        mockProjectVo.setLng(-122.0843428);

        when(openApiProjectService.getProject(userId, projectId)).thenReturn(mockProjectVo);

        // Act & Assert
        MvcResult result = mockMvc.perform(get("/v1/project/{projectId}", projectId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.project[0].id").value(projectId))
                .andExpect(jsonPath("$.project[0].streetAddress").value("11 Howard St"))
                .andExpect(jsonPath("$.project[0].city").value("New York"))
                .andExpect(jsonPath("$.project[0].state").value("NY"))
                .andExpect(jsonPath("$.project[0].lat").value("37.4222804"))
                .andExpect(jsonPath("$.project[0].lng").value("-122.0843428"))
            .andReturn();

        assertEquals(MediaType.APPLICATION_JSON.toString(),
                result.getResponse().getContentType());
    }

    @Test
    void testGetProject_ShouldThrowException_WhenProjectNotFound() throws Exception {
        // Arrange
        long userId = 10001L;
        long projectId = 99999L;

        when(openApiProjectService.getProject(anyLong(), anyLong()))
                .thenThrow(new ResourceNotFoundException());

        // Act & Assert
        mockMvc.perform(get("/v1/project/{projectId}", projectId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value("404"));
    }
}
