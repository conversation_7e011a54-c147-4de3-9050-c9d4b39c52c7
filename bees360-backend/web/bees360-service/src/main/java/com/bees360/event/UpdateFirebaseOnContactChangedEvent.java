package com.bees360.event;

import com.bees360.event.registry.ContactChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.ContactManager;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.util.JsonUtils;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听联系人变更事件并更新Firebase中的联系人数据
 */
@Log4j2
public class UpdateFirebaseOnContactChangedEvent extends AbstractNamedEventListener<ContactChangedEvent> {

    private final FirebaseProjectService firebaseProjectService;
    private final ContactManager contactManager;

    public UpdateFirebaseOnContactChangedEvent(
            @NonNull FirebaseProjectService firebaseProjectService,
            @NonNull ContactManager contactManager) {
        this.firebaseProjectService = firebaseProjectService;
        this.contactManager = contactManager;
        log.info("Created {}(firebaseProjectService={}, contactManager={})",
            this, firebaseProjectService, contactManager);
    }

    @Override
    public void handle(ContactChangedEvent event) throws IOException {
        log.info("Update firebase on firebase contact changed event {}", JsonUtils.toJson(event));
        var contacts = contactManager.findByProjectId(event.getProjectId());
        firebaseProjectService.updateContacts(Long.parseLong(event.getProjectId()), contacts);
    }
}
