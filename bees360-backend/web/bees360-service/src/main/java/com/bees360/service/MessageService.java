package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.ContactUs;
import com.bees360.entity.DailyBatchProjectModel;
import com.bees360.entity.DailyBatchStatusModel;
import com.bees360.entity.PartnerProgram;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.User;
import com.bees360.entity.dto.ProjectStatistics;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.firebase.entity.FirebaseFeedback;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectRoleWorkServiceOuterClass;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface MessageService {

	void sendErrorToEngineer(String message, Throwable e) throws ServiceException;

	void shareReport(long projectId, String reportId, long senderId, List<String> recipients) throws ServiceException;

    void infoStaffsImageUploaded(Project project, int imageNum);

	/**
	 * send email and sms message to all users of the project to let them know the report has been approved.
	 * @param projectId
	 * @param recipients 邮件接收人
	 * @param reportType 通过审核的报告类别
	 * @param report 通过审核的报告，作为邮件的附件，如果为null时候，不作为附件
	 * @throws ServiceException
	 */
	void infoReportApproved(long projectId, List<UserTinyVo> recipients, ReportTypeEnum reportType,
                            ProjectReportFile report, boolean withImagesZipLink) throws ServiceException;

    void infoReportOnClientReceived(long projectId, List<UserTinyVo> recipients, List<ProjectReportFile> reports, boolean withImagesZipLink);

	void infoInviteAsVisitor(long projectId,  long invitor, long userId) throws ServiceException;

	void infoReportSubmitted(long projectId, ReportTypeEnum reportType, UserTinyVo recipient) throws ServiceException;

	void infoReportDisapproved(long projectId, ReportTypeEnum reportType, UserTinyVo recipient) throws ServiceException;

	void sendContactUS(ContactUs contactUs) throws ServiceException;

	void infoMemberArranged(long projectId, UserTinyVo tinyProcessor, RoleEnum reviewer) throws ServiceException;

	void infoMemberArrangementCancel(Project project, UserTinyVo worker, RoleEnum role) throws ServiceException;

	void infoProjectDeleted(Project project, UserTinyVo operator, UserTinyVo reciption) throws ServiceException;

	void infoPartnerProgram(PartnerProgram partnerProgram) throws ServiceException;

	void infoPartnerProgramRecieved(PartnerProgram partnerProgram) throws ServiceException;

    void sendProjectCreated(User creator, Project project, List<String> recipients);

    /**
     * 给户主发送邮件，通知已经安排好飞手了
     * @param project 分配飞手的项目
     * @param pilot 角色为飞手的用户
     */
    void sendAdToHouseOwner(Project project, User pilot);

    void sendToPilotArranged(UserTinyVo infoRecipient, List<Project> projects, BeesPilotBatch pilotBatch);

    void sendToPilotDailyNotify(UserTinyVo infoRecipient, List<Project> collect);

    void infoProjectReceiveError(Project project, String comment);


    void sendSmsToNotifyPilot(User user, String content) throws ServiceException;

    void infoAdminPilotFeedback(User pilot, UserTinyVo infoRecipient, Long projectId, FirebaseFeedback feedback);

    void weekSendPilotTaskNeedPayedEmailToAdmin(List<String> adminEmails, String curDate, String startDate, String endDate,
                                                List<DailyBatchProjectModel> projectModelList,
                                                List<DailyBatchStatusModel> statusModelList);

    void sendToAdminForHoverError(Collection<String> recipients, Long projectId, boolean hoverAuthorizationError, boolean hoverServerError);

    void infoAdminRoleWorkWeeklyCount(String startDate, String endDate, List<ProjectRoleWorkServiceOuterClass.ProjectWorkItem> claimList,
                                      List<ProjectRoleWorkServiceOuterClass.ProjectWorkItem> underwritingList);

    void sendProjectStatisticsPerCompany(Map<InspectionPurposeTypeEnum, ProjectStatistics.ProjectStatsPerCompanySummary> summaries, LocalDate statisticsDate);

    void sendProjectAlerts(ZonedDateTime start, ZonedDateTime end);

    void sendEmailNoticeOnEmergencyCase(String content, Project project);
}
